# Client Timestamp Validation - UTC Everywhere, Local Display

## Resumo

Implementação de validação de timestamp do cliente para garantir que as datas base usadas nos cálculos de assinatura sejam precisas e consistentes, seguindo o padrão **UTC Everywhere, Local Display**.

## Problema Resolvido

- **Inconsistência de timezone**: Cliente e servidor em timezones diferentes causavam cálculos incorretos
- **Datas base incorretas**: Assinaturas criadas com data base do servidor em vez da data real do cliente
- **Bugs de billing**: Diferenças de timezone causavam problemas nos cálculos de billing dates

## Solução Implementada

### 1. Frontend - Adição Automática de Timestamp

#### Hook useDate Atualizado
```typescript
// Novas funções adicionadas
const getClientTimestamp = (): string => {
  return new Date().toISOString();
};

const createPayloadWithTimestamp = (payload: any): any => {
  return {
    ...payload,
    clientTimestamp: getClientTimestamp(),
  };
};
```

#### Uso no Frontend
```typescript
// Automático
const payloadWithTimestamp = createPayloadWithTimestamp(paymentData);

// Manual
const payload = {
  ...paymentData,
  clientTimestamp: getClientTimestamp(),
};
```

### 2. Backend - Validação e Uso

#### DTO Atualizado
```typescript
export class CreatePaymentDto {
  // ... outros campos

  @ApiProperty({ 
    description: 'Client current timestamp in ISO format',
    example: '2024-01-15T10:30:00.000Z',
    required: false
  })
  @IsOptional()
  @IsDateString()
  @TransformToUTC()
  @IsValidClientTimestamp(1) // Máximo 1 dia de diferença
  clientTimestamp?: string;
}
```

#### Validador Customizado
```typescript
@ValidatorConstraint({ name: 'isValidClientTimestamp', async: false })
export class IsValidClientTimestampConstraint implements ValidatorConstraintInterface {
  validate(clientTimestamp: string, args: ValidationArguments) {
    const clientDate = new Date(clientTimestamp);
    const serverDate = this.dateService.now();
    
    const diffMs = Math.abs(serverDate.getTime() - clientDate.getTime());
    const diffDays = diffMs / (24 * 60 * 60 * 1000);
    
    const maxDaysDifference = args.constraints[0] || 1;
    return diffDays <= maxDaysDifference;
  }
}
```

#### Service de Validação
```typescript
@Injectable()
export class ClientTimestampValidator {
  validateTimestamp(clientTimestamp: string, maxDaysDifference: number = 1): {
    isValid: boolean;
    serverTime: string;
    clientTime: string;
    differenceMs: number;
    differenceDays: number;
    error?: string;
  }

  getBaseDate(clientTimestamp?: string, maxDaysDifference: number = 1): Date {
    if (!clientTimestamp) {
      return this.dateService.now();
    }

    const validation = this.validateTimestamp(clientTimestamp, maxDaysDifference);
    
    if (validation.isValid) {
      return new Date(validation.clientTime);
    } else {
      console.warn('Client timestamp validation failed:', validation.error);
      return this.dateService.now();
    }
  }
}
```

#### PaymentService Atualizado
```typescript
async subscribe(createPaymentDto: CreatePaymentDto, account: Account) {
  let { paymentMethod, accountId, isYearly, billingDay, clientTimestamp } = createPaymentDto;

  // Get base date from client timestamp (validated) or server time
  const baseDate = this.clientTimestampValidator.getBaseDate(clientTimestamp, 1);
  
  if (paymentMethod === PaymentMethod.CREDIT_CARD) {
    billingDay = baseDate.getUTCDate(); // Use base date instead of server time
  }

  // Log validation info for debugging
  if (clientTimestamp) {
    const validation = this.clientTimestampValidator.validateTimestamp(clientTimestamp, 1);
    Logger.log(`Client timestamp validation: ${validation.isValid ? 'VALID' : 'INVALID'}`);
  }

  const newSubscription = createSubscriptionPlan({
    plan,
    createPaymentDto,
    account: account,
    customerId,
    baseDate, // Pass the validated base date
  });
  
  // ... resto da lógica
}
```

## Fluxo de Funcionamento

### 1. Frontend
1. Usuário preenche formulário de pagamento
2. Frontend automaticamente adiciona `clientTimestamp` atual em UTC
3. Payload é enviado para o backend

### 2. Backend
1. DTO valida que `clientTimestamp` está dentro de 1 dia do servidor
2. Se válido: usa timestamp do cliente como data base
3. Se inválido/ausente: usa data do servidor como fallback
4. Data base é usada para todos os cálculos de assinatura

### 3. Validação
- **Máximo 1 dia de diferença** entre cliente e servidor
- **Fallback automático** para data do servidor se inválido
- **Logs detalhados** para debugging

## Benefícios

### 1. Precisão
- Assinaturas criadas com data real do cliente
- Billing dates calculados corretamente
- Eliminação de bugs de timezone

### 2. Robustez
- Validação automática de timestamps
- Fallback para data do servidor
- Tolerância a diferenças pequenas de tempo

### 3. Transparência
- Logs detalhados de validação
- Informações de debugging disponíveis
- Fácil identificação de problemas

## Casos de Uso

### 1. Cliente e Servidor no Mesmo Timezone
```
Cliente: 2024-01-15T10:30:00.000Z
Servidor: 2024-01-15T10:30:05.000Z
Diferença: 5 segundos
Resultado: ✅ VÁLIDO - Usa timestamp do cliente
```

### 2. Cliente e Servidor em Timezones Diferentes
```
Cliente: 2024-01-15T10:30:00.000Z (Brasil)
Servidor: 2024-01-15T13:30:00.000Z (UTC)
Diferença: 0 segundos (ambos em UTC)
Resultado: ✅ VÁLIDO - Usa timestamp do cliente
```

### 3. Diferença Maior que 1 Dia
```
Cliente: 2024-01-15T10:30:00.000Z
Servidor: 2024-01-17T10:30:00.000Z
Diferença: 2 dias
Resultado: ❌ INVÁLIDO - Usa timestamp do servidor
```

### 4. Timestamp Ausente
```
Cliente: undefined
Servidor: 2024-01-15T10:30:00.000Z
Resultado: ✅ FALLBACK - Usa timestamp do servidor
```

## Arquivos Implementados

### Backend
- `src/common/dto/client-timestamp.dto.ts` - DTOs base
- `src/common/validators/client-timestamp.validator.ts` - Validador customizado
- `src/modules/payment/dto/create-payment.dto.ts` - DTO atualizado
- `src/modules/payment/payment.service.ts` - Service atualizado
- `src/utils/subscription.helper.ts` - Helper atualizado

### Frontend
- `src/hooks/useDate.ts` - Hook atualizado
- `src/contexts/checkout/CheckoutContext.tsx` - Context atualizado
- `src/components/examples/ClientTimestampExample.tsx` - Exemplo de uso

## Testes Recomendados

### 1. Teste de Validação
```typescript
describe('ClientTimestampValidator', () => {
  it('should accept timestamps within 1 day', () => {
    const clientTime = new Date().toISOString();
    const validation = validator.validateTimestamp(clientTime, 1);
    expect(validation.isValid).toBe(true);
  });

  it('should reject timestamps older than 1 day', () => {
    const clientTime = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString();
    const validation = validator.validateTimestamp(clientTime, 1);
    expect(validation.isValid).toBe(false);
  });
});
```

### 2. Teste de Integração
```typescript
describe('PaymentService.subscribe', () => {
  it('should use client timestamp as base date when valid', async () => {
    const clientTimestamp = new Date().toISOString();
    const dto = { ...createPaymentDto, clientTimestamp };
    
    const result = await paymentService.subscribe(dto, account);
    
    // Verificar que a assinatura foi criada com a data do cliente
    expect(result.data.startDate).toContain(clientTimestamp.split('T')[0]);
  });
});
```

## Monitoramento

### Logs de Validação
```
Client timestamp validation: VALID - Server: 2024-01-15T10:30:00.000Z, Client: 2024-01-15T10:30:00.000Z, Diff: 0.00 days
```

### Métricas Recomendadas
- Taxa de timestamps válidos vs inválidos
- Diferença média entre cliente e servidor
- Frequência de uso do fallback

## Próximos Passos

1. **Implementar em outros endpoints** que criam entidades com datas
2. **Adicionar métricas** de validação no dashboard
3. **Criar alertas** para diferenças grandes de tempo
4. **Expandir validação** para outros tipos de timestamp

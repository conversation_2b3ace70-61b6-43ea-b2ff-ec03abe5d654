# Migração para Timestamps - Assinaturas e Faturas

## Resumo das Mudanças

Este documento descreve as alterações realizadas para padronizar o tratamento de datas no sistema, fazendo com que o backend trabalhe com timestamps (em milissegundos) e o frontend converta para o horário do usuário usando métodos do Firebase.

## Arquivos Modificados

### Backend

#### 1. `backend/src/modules/subscription/subscription.service.ts`

- **Mudanças**: Convertido todas as datas de `toDate().toISOString()` para `toMillis()`
- **Linhas alteradas**:
  - 225-238: Conversão de datas nas subscriptions
  - 259: Conversão de next_billing_date
  - 300-306: Conversão de datas da subscription individual
  - 314-323: Conversão de datas das invoices
- **Impacto**: Agora retorna timestamps em milissegundos em vez de strings ISO

#### 2. `backend/src/modules/pagarme/types/subscriptions.details.ts`

- **Mudanças**: Alterado tipo de `next_billing_date` de `string` para `number`
- **Linha alterada**: 17
- **Impacto**: Interface agora espera timestamp numérico

#### 3. `backend/src/modules/subscription/types/qiplus.types.ts`

- **Mudanças**: Alterado tipos de `due_at` de `Timestamp` para `number` nos tipos de invoice
- **Linhas alteradas**: 130, 137
- **Impacto**: Tipos de invoice agora usam timestamps numéricos

#### 4. `backend/src/modules/webhook/webhook.controller.ts`

- **Mudanças**: Convertido timestamps para milissegundos nas invoices
- **Linhas alteradas**: 233-235, 257
- **Impacto**: Webhooks agora salvam timestamps como números

#### 5. `backend/src/modules/subscription/subscription.controller.ts`

- **Mudanças**: Convertido retornos de subscription para usar timestamps em milissegundos
- **Linhas alteradas**: 55-85, 161-170, 211-220
- **Métodos afetados**: `getSubscription`, `cancelSubscription`, `activateSubscription`
- **Impacto**: Endpoints agora retornam timestamps numéricos em vez de strings ISO

### Frontend

#### 1. `frontend/src/types/invoice.types.ts`

- **Mudanças**: Alterado tipo de `due_at` de `string` para `number`
- **Linhas alteradas**: 8, 32
- **Impacto**: Interfaces agora esperam timestamps numéricos

#### 2. `frontend/src/types/backend/qiplus.types.ts`

- **Mudanças**: Alterado tipos de datas de `string` para `number`
- **Linhas alteradas**: 107, 122, 130, 137
- **Impacto**: Tipos de subscription e invoice agora usam timestamps

#### 3. `frontend/src/lib/date.helper.ts`

- **Mudanças**: Adicionados métodos para conversão de timestamps
- **Métodos adicionados**:
  - `fromTimestampMs(timestampMs: number): Date`
  - `fromTimestampMsToFirebase(timestampMs: number): Timestamp`
- **Impacto**: Facilita conversão entre timestamps e objetos Date

#### 4. `frontend/src/lib/invoice.utils.ts`

- **Mudanças**: Atualizada função `formatCycleDate` para aceitar timestamps
- **Impacto**: Agora aceita `string | number | Date` como parâmetro

#### 5. `frontend/src/components/invoices/InvoiceTable.tsx`

- **Mudanças**: Removida função local de formatação, usando a do utils
- **Impacto**: Usa formatação centralizada que suporta timestamps

#### 6. `frontend/src/pages/Subscription.tsx`

- **Mudanças**: Atualizada função `formatCycleDate` para suportar timestamps
- **Impacto**: Suporta tanto strings quanto números para datas

## Como Funciona Agora

### Backend

1. **Armazenamento**: Datas continuam sendo armazenadas como Firebase Timestamps no banco
2. **Processamento**: Internamente usa Timestamps do Firebase
3. **API Response**: Converte para milissegundos usando `toMillis()` antes de enviar para o frontend

### Frontend

1. **Recebimento**: Recebe timestamps em milissegundos das APIs
2. **Conversão**: Usa `new Date(timestamp)` ou métodos do DateHelper para converter
3. **Exibição**: Formata usando `date-fns` com locale pt-BR para o timezone do usuário

## Benefícios

1. **Consistência**: Todas as datas seguem o mesmo padrão
2. **Timezone**: Frontend converte automaticamente para o timezone do usuário
3. **Performance**: Timestamps são mais eficientes que strings ISO
4. **Manutenibilidade**: Lógica de conversão centralizada

## Compatibilidade

- ✅ **Backward Compatible**: Funções de formatação suportam tanto strings quanto timestamps
- ✅ **Firebase**: Continua usando Timestamps internamente no backend
- ✅ **Timezone**: Conversão automática para timezone do usuário no frontend

## Próximos Passos

1. Testar as mudanças em ambiente de desenvolvimento
2. Verificar se todas as datas estão sendo exibidas corretamente
3. Validar que os timezones estão sendo aplicados adequadamente
4. Executar testes de integração para garantir compatibilidade

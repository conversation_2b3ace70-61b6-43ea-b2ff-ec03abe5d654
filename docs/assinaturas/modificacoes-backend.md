# Modificações Necessárias - Backend

## PRIORIDADE CRÍTICA

### 1. Sistema de Scheduler de Ativação

**Arquivo**: `backend/src/modules/subscription/subscription-scheduler.service.ts`

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { SubscriptionService } from './subscription.service';
import { AccountService } from '../account/account.service';
import { QISubscription, QISubscriptionStatus, QIScheduledAction } from './types/qiplus.types';
import * as admin from 'firebase-admin';

@Injectable()
export class SubscriptionSchedulerService {
  private readonly logger = new Logger(SubscriptionSchedulerService.name);

  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly accountService: AccountService,
  ) {}

  @Cron('0 0 * * *') // Todo dia à meia-noite
  async processScheduledActions() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    this.logger.log('Iniciando processamento de ações agendadas');
    
    try {
      // Processar ativações agendadas
      await this.processScheduledActivations(today);
      
      // Processar cancelamentos agendados
      await this.processScheduledCancellations(today);
      
      // Processar expirações
      await this.processScheduledExpirations(today);
      
      this.logger.log('Processamento de ações agendadas concluído');
    } catch (error) {
      this.logger.error('Erro ao processar ações agendadas', error);
    }
  }
  
  private async processScheduledActivations(date: Date) {
    const subscriptionsToActivate = await this.subscriptionService
      .getSubscriptionsToActivate(date);
    
    this.logger.log(`Processando ${subscriptionsToActivate.length} ativações agendadas`);
    
    for (const subscription of subscriptionsToActivate) {
      try {
        await this.activateSubscription(subscription);
        this.logger.log(`Assinatura ativada: ${subscription.id}`);
      } catch (error) {
        this.logger.error(`Erro ao ativar assinatura ${subscription.id}`, error);
      }
    }
  }
  
  private async processScheduledCancellations(date: Date) {
    const subscriptionsToCancel = await this.subscriptionService
      .getSubscriptionsToCancel(date);
    
    this.logger.log(`Processando ${subscriptionsToCancel.length} cancelamentos agendados`);
    
    for (const subscription of subscriptionsToCancel) {
      try {
        await this.cancelSubscription(subscription);
        this.logger.log(`Assinatura cancelada: ${subscription.id}`);
      } catch (error) {
        this.logger.error(`Erro ao cancelar assinatura ${subscription.id}`, error);
      }
    }
  }
  
  private async processScheduledExpirations(date: Date) {
    const subscriptionsToExpire = await this.subscriptionService
      .getSubscriptionsToExpire(date);
    
    this.logger.log(`Processando ${subscriptionsToExpire.length} expirações agendadas`);
    
    for (const subscription of subscriptionsToExpire) {
      try {
        await this.expireSubscription(subscription);
        this.logger.log(`Assinatura expirada: ${subscription.id}`);
      } catch (error) {
        this.logger.error(`Erro ao expirar assinatura ${subscription.id}`, error);
      }
    }
  }
  
  private async activateSubscription(subscription: QISubscription) {
    // Ativar nova assinatura
    await this.subscriptionService.updateSubscription(subscription.id, {
      status: QISubscriptionStatus.ACTIVE,
      scheduledAction: null,
      activatedAt: admin.firestore.Timestamp.now(),
    });
    
    // Atualizar conta com novo plano
    await this.accountService.updateAccountPlan(
      subscription.accountId,
      subscription.planId
    );
    
    // Cancelar assinatura anterior se existir
    if (subscription.metadata?.previousSubscriptionId) {
      await this.subscriptionService.updateSubscription(
        subscription.metadata.previousSubscriptionId,
        {
          status: QISubscriptionStatus.CANCELED,
          canceledAt: admin.firestore.Timestamp.now(),
        }
      );
    }
    
    this.logger.log(`Assinatura ${subscription.id} ativada com sucesso`);
  }
  
  private async cancelSubscription(subscription: QISubscription) {
    await this.subscriptionService.updateSubscription(subscription.id, {
      status: QISubscriptionStatus.CANCELED,
      scheduledAction: null,
      canceledAt: admin.firestore.Timestamp.now(),
    });
  }
  
  private async expireSubscription(subscription: QISubscription) {
    await this.subscriptionService.updateSubscription(subscription.id, {
      status: QISubscriptionStatus.EXPIRED,
      scheduledAction: null,
      expiredAt: admin.firestore.Timestamp.now(),
    });
    
    // Atualizar conta para plano gratuito
    await this.accountService.updateAccountToFreePlan(subscription.accountId);
  }
}
```

### 2. Regras Consistentes de Upgrade

**Arquivo**: `backend/src/modules/upgrade/upgrade-rules.service.ts`

```typescript
import { Injectable } from '@nestjs/common';
import { QISubscription, QIBillingInterval } from '../subscription/types/qiplus.types';
import { CreatePaymentDto } from '../payment/dto/create-payment.dto';

export interface UpgradeApplicationRule {
  type: 'IMMEDIATE' | 'SCHEDULED';
  reason: 'BILLING_CYCLE_CHANGE' | 'SAME_CYCLE_UPGRADE' | 'DOWNGRADE' | 'CUSTOMIZATION';
  activationDate: Date;
  immediateCharge: boolean;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

@Injectable()
export class UpgradeRulesService {
  
  determineUpgradeApplication(
    currentSubscription: QISubscription,
    createPaymentDto: CreatePaymentDto,
    upgradeValue: number
  ): UpgradeApplicationRule {
    
    const isChangingBillingCycle = 
      currentSubscription.billingInterval === QIBillingInterval.YEARLY !== createPaymentDto.isYearly;
    
    const isUpgrade = upgradeValue > 0;
    const isDowngrade = upgradeValue < 0;
    const isSameValue = upgradeValue === 0;
    
    // REGRA 1: Mudança de ciclo de faturamento sempre é agendada
    if (isChangingBillingCycle) {
      return {
        type: 'SCHEDULED',
        reason: 'BILLING_CYCLE_CHANGE',
        activationDate: currentSubscription.currentPeriodEnd.toDate(),
        immediateCharge: false,
        message: 'Upgrade será aplicado no fim do período atual devido à mudança de ciclo de faturamento'
      };
    }
    
    // REGRA 2: Upgrade no mesmo ciclo é imediato
    if (isUpgrade) {
      return {
        type: 'IMMEDIATE',
        reason: 'SAME_CYCLE_UPGRADE',
        activationDate: new Date(),
        immediateCharge: true,
        message: 'Upgrade aplicado imediatamente após o pagamento'
      };
    }
    
    // REGRA 3: Downgrade sempre é agendado
    if (isDowngrade) {
      return {
        type: 'SCHEDULED',
        reason: 'DOWNGRADE',
        activationDate: currentSubscription.currentPeriodEnd.toDate(),
        immediateCharge: false,
        message: 'Downgrade será aplicado no fim do período atual'
      };
    }
    
    // REGRA 4: Mesmo valor (customização) é imediato
    if (isSameValue) {
      return {
        type: 'IMMEDIATE',
        reason: 'CUSTOMIZATION',
        activationDate: new Date(),
        immediateCharge: false,
        message: 'Alterações aplicadas imediatamente'
      };
    }
    
    // Fallback - não deveria chegar aqui
    return {
      type: 'SCHEDULED',
      reason: 'DOWNGRADE',
      activationDate: currentSubscription.currentPeriodEnd.toDate(),
      immediateCharge: false,
      message: 'Alteração será aplicada no fim do período atual'
    };
  }
  
  validateUpgradeRules(
    currentSubscription: QISubscription,
    createPaymentDto: CreatePaymentDto
  ): ValidationResult {
    const errors: string[] = [];
    
    // Validar se pode fazer upgrade
    if (currentSubscription.status !== 'active') {
      errors.push('Apenas assinaturas ativas podem ser atualizadas');
    }
    
    // Validar se não está tentando fazer upgrade para o mesmo plano
    if (currentSubscription.planId === createPaymentDto.planId) {
      // Permitir apenas se houver mudança em customFeatures ou additionals
      const hasCustomChanges = this.hasCustomizationChanges(currentSubscription, createPaymentDto);
      if (!hasCustomChanges) {
        errors.push('Selecione um plano diferente do atual');
      }
    }
    
    // Validar mudança de método de pagamento
    if (currentSubscription.paymentMethod !== createPaymentDto.paymentMethod) {
      if (!this.isPaymentMethodChangeAllowed(currentSubscription, createPaymentDto)) {
        errors.push('Mudança de método de pagamento não permitida para este tipo de upgrade');
      }
    }
    
    // Validar parcelamento
    if (createPaymentDto.installments > 1 && !createPaymentDto.isYearly) {
      errors.push('Parcelamento disponível apenas para planos anuais');
    }
    
    if (createPaymentDto.installments > 12) {
      errors.push('Máximo de 12 parcelas permitidas');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  private hasCustomizationChanges(
    currentSubscription: QISubscription,
    createPaymentDto: CreatePaymentDto
  ): boolean {
    // Verificar mudanças em customFeatures
    const currentCustomFeatures = currentSubscription.items
      .filter(item => item.type === 'customFeature')
      .map(item => ({ id: item.id, quantity: item.quantity }));
    
    const newCustomFeatures = createPaymentDto.customFeatures || [];
    
    if (currentCustomFeatures.length !== newCustomFeatures.length) {
      return true;
    }
    
    // Verificar mudanças em additionals
    const currentAdditionals = currentSubscription.items
      .filter(item => item.type === 'additional')
      .map(item => item.id);
    
    const newAdditionals = createPaymentDto.additionals || [];
    
    if (currentAdditionals.length !== newAdditionals.length) {
      return true;
    }
    
    // Verificar mudanças na quantidade de leads
    const currentLeadsCount = currentSubscription.items
      .find(item => item.type === 'plan')?.quantity || 0;
    
    if (currentLeadsCount !== createPaymentDto.leadsCount) {
      return true;
    }
    
    return false;
  }
  
  private isPaymentMethodChangeAllowed(
    currentSubscription: QISubscription,
    createPaymentDto: CreatePaymentDto
  ): boolean {
    // Por enquanto, permitir mudança de método apenas em casos específicos
    // Pode ser expandido conforme necessário
    return true;
  }
}
```

### 3. Melhorar Webhook Handler

**Modificar**: `backend/src/modules/webhook/webhook.service.ts`

```typescript
// Adicionar método para processar webhooks de upgrade
async handlePaymentWebhook(webhookData: any) {
  const { subscriptionId, status, installmentNumber, orderId } = webhookData;
  
  if (status === 'paid') {
    const subscription = await this.subscriptionService.getSubscription(subscriptionId);
    
    if (!subscription) {
      Logger.error(`Assinatura não encontrada: ${subscriptionId}`);
      return;
    }
    
    // Verificar se é upgrade imediato
    const upgradeRule = subscription.metadata?.upgradeRule;
    
    if (upgradeRule?.type === 'IMMEDIATE' && installmentNumber === 1) {
      await this.applyUpgradeImmediately(subscription);
    }
    
    // Atualizar status da assinatura
    await this.updateSubscriptionPaymentStatus(subscription, webhookData);
    
    Logger.log(`Webhook processado para assinatura: ${subscriptionId}`);
  }
}

private async applyUpgradeImmediately(subscription: QISubscription) {
  try {
    // Ativar novo plano na conta
    await this.accountService.updateAccountPlan(
      subscription.accountId,
      subscription.planId
    );
    
    // Cancelar assinatura anterior se existir
    if (subscription.metadata?.previousSubscriptionId) {
      await this.subscriptionService.updateSubscription(
        subscription.metadata.previousSubscriptionId,
        {
          status: QISubscriptionStatus.CANCELED,
          canceledAt: admin.firestore.Timestamp.now(),
        }
      );
    }
    
    // Ativar nova assinatura
    await this.subscriptionService.updateSubscription(subscription.id, {
      status: QISubscriptionStatus.ACTIVE,
      activatedAt: admin.firestore.Timestamp.now(),
    });
    
    Logger.log(`Upgrade aplicado imediatamente: ${subscription.id}`);
    
  } catch (error) {
    Logger.error(`Erro ao aplicar upgrade imediato: ${subscription.id}`, error);
    throw error;
  }
}

private async updateSubscriptionPaymentStatus(
  subscription: QISubscription,
  webhookData: any
) {
  // Atualizar status do pagamento na assinatura
  const updateData: Partial<QISubscription> = {
    metadata: {
      ...subscription.metadata,
      lastPaymentStatus: webhookData.status,
      lastPaymentDate: new Date().toISOString(),
      lastOrderId: webhookData.orderId,
    }
  };
  
  await this.subscriptionService.updateSubscription(subscription.id, updateData);
}
```

## PRIORIDADE ALTA

### 4. Sistema de Cobrança Automática

**Arquivo**: `backend/src/modules/billing/billing-scheduler.service.ts`

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { SubscriptionService } from '../subscription/subscription.service';
import { PaymentService } from '../payment/payment.service';
import { QISubscription, QIPaymentMethod } from '../subscription/types/qiplus.types';
import * as admin from 'firebase-admin';

@Injectable()
export class BillingSchedulerService {
  private readonly logger = new Logger(BillingSchedulerService.name);

  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly paymentService: PaymentService,
  ) {}

  @Cron('0 6 * * *') // Todo dia às 6h da manhã
  async processPreBillingCharges() {
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
    
    this.logger.log('Iniciando processamento de cobranças antecipadas');
    
    try {
      // Buscar assinaturas que vencem em 3 dias
      const subscriptionsToCharge = await this.subscriptionService
        .getSubscriptionsWithBillingDate(threeDaysFromNow);
      
      this.logger.log(`Encontradas ${subscriptionsToCharge.length} assinaturas para cobrança antecipada`);
      
      for (const subscription of subscriptionsToCharge) {
        try {
          await this.processPreBillingCharge(subscription);
        } catch (error) {
          this.logger.error(`Erro ao processar cobrança antecipada: ${subscription.id}`, error);
        }
      }
      
      this.logger.log('Processamento de cobranças antecipadas concluído');
    } catch (error) {
      this.logger.error('Erro no processamento de cobranças antecipadas', error);
    }
  }
  
  private async processPreBillingCharge(subscription: QISubscription) {
    // Verificar se já foi processada
    if (subscription.metadata?.preBillingProcessed) {
      return;
    }
    
    this.logger.log(`Processando cobrança antecipada para: ${subscription.id}`);
    
    // Gerar cobrança baseada no método de pagamento
    switch (subscription.paymentMethod) {
      case QIPaymentMethod.BOLETO:
      case QIPaymentMethod.PIX:
        await this.generatePreBillingCharge(subscription);
        break;
      case QIPaymentMethod.CREDIT_CARD:
        // Cartão é processado automaticamente no vencimento
        await this.scheduleCardCharge(subscription);
        break;
    }
    
    // Marcar como processada
    await this.subscriptionService.updateSubscription(subscription.id, {
      metadata: {
        ...subscription.metadata,
        preBillingProcessed: true,
        preBillingDate: new Date().toISOString(),
      }
    });
  }
  
  private async generatePreBillingCharge(subscription: QISubscription) {
    try {
      // Criar pedido de renovação
      const renewalOrder = await this.paymentService.createRenewalOrder(subscription);
      
      this.logger.log(`Pedido de renovação criado: ${renewalOrder.id} para assinatura: ${subscription.id}`);
      
      // Agendar retry se não pago em 3 dias
      await this.scheduleRetryIfNotPaid(subscription.id, renewalOrder.id);
      
    } catch (error) {
      this.logger.error(`Erro ao gerar cobrança antecipada para ${subscription.id}`, error);
      throw error;
    }
  }
  
  private async scheduleCardCharge(subscription: QISubscription) {
    // Para cartão, apenas agendar a cobrança para o dia do vencimento
    this.logger.log(`Cobrança de cartão agendada para: ${subscription.id}`);
    
    await this.subscriptionService.updateSubscription(subscription.id, {
      metadata: {
        ...subscription.metadata,
        cardChargeScheduled: true,
        cardChargeDate: subscription.nextBillingDate.toDate().toISOString(),
      }
    });
  }
  
  private async scheduleRetryIfNotPaid(subscriptionId: string, orderId: string) {
    // Implementar lógica de retry
    // Por exemplo, verificar em 24h se o pagamento foi efetuado
    // Se não foi, enviar lembrete e agendar novo retry
    
    this.logger.log(`Retry agendado para assinatura: ${subscriptionId}, pedido: ${orderId}`);
  }
}
```

### 5. Adicionar Métodos no SubscriptionService

**Modificar**: `backend/src/modules/subscription/subscription.service.ts`

```typescript
// Adicionar métodos para o scheduler
async getSubscriptionsToActivate(date: Date): Promise<QISubscription[]> {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  return this.subscriptionRepository.getSubscriptionsWhere([
    {
      field: 'scheduledAction',
      operator: '==',
      value: QIScheduledAction.ACTIVATE,
    },
    {
      field: 'startDate',
      operator: '>=',
      value: admin.firestore.Timestamp.fromDate(startOfDay),
    },
    {
      field: 'startDate',
      operator: '<=',
      value: admin.firestore.Timestamp.fromDate(endOfDay),
    },
  ]);
}

async getSubscriptionsToCancel(date: Date): Promise<QISubscription[]> {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  return this.subscriptionRepository.getSubscriptionsWhere([
    {
      field: 'scheduledAction',
      operator: '==',
      value: QIScheduledAction.CANCEL,
    },
    {
      field: 'currentPeriodEnd',
      operator: '>=',
      value: admin.firestore.Timestamp.fromDate(startOfDay),
    },
    {
      field: 'currentPeriodEnd',
      operator: '<=',
      value: admin.firestore.Timestamp.fromDate(endOfDay),
    },
  ]);
}

async getSubscriptionsToExpire(date: Date): Promise<QISubscription[]> {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  return this.subscriptionRepository.getSubscriptionsWhere([
    {
      field: 'scheduledAction',
      operator: '==',
      value: QIScheduledAction.EXPIRE,
    },
    {
      field: 'currentPeriodEnd',
      operator: '>=',
      value: admin.firestore.Timestamp.fromDate(startOfDay),
    },
    {
      field: 'currentPeriodEnd',
      operator: '<=',
      value: admin.firestore.Timestamp.fromDate(endOfDay),
    },
  ]);
}

async getSubscriptionsWithBillingDate(date: Date): Promise<QISubscription[]> {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  return this.subscriptionRepository.getSubscriptionsWhere([
    {
      field: 'status',
      operator: '==',
      value: QISubscriptionStatus.ACTIVE,
    },
    {
      field: 'nextBillingDate',
      operator: '>=',
      value: admin.firestore.Timestamp.fromDate(startOfDay),
    },
    {
      field: 'nextBillingDate',
      operator: '<=',
      value: admin.firestore.Timestamp.fromDate(endOfDay),
    },
  ]);
}
```

## ARQUIVOS A SEREM CRIADOS

### Novos Serviços (5)
1. `backend/src/modules/subscription/subscription-scheduler.service.ts`
2. `backend/src/modules/upgrade/upgrade-rules.service.ts`
3. `backend/src/modules/billing/billing-scheduler.service.ts`
4. `backend/src/modules/billing/billing-scheduler.module.ts`
5. `backend/src/common/jobs/subscription-jobs.service.ts`

### DTOs e Types (3)
1. `backend/src/modules/upgrade/types/upgrade-rules.types.ts`
2. `backend/src/modules/billing/dto/billing-notification.dto.ts`
3. `backend/src/modules/subscription/types/scheduler.types.ts`

### Testes (3)
1. `backend/src/modules/subscription/subscription-scheduler.service.spec.ts`
2. `backend/src/modules/upgrade/upgrade-rules.service.spec.ts`
3. `backend/src/modules/billing/billing-scheduler.service.spec.ts`

## ARQUIVOS A SEREM MODIFICADOS

### Modificações Obrigatórias (5)
1. `backend/src/modules/subscription/subscription.service.ts` - Adicionar métodos de scheduler
2. `backend/src/modules/upgrade/upgrade.service.ts` - Integrar regras consistentes
3. `backend/src/modules/webhook/webhook.service.ts` - Melhorar handler de pagamento
4. `backend/src/modules/payment/payment.service.ts` - Integrar scheduler de cobrança
5. `backend/src/app.module.ts` - Registrar novos módulos e schedulers

## DEPENDÊNCIAS NECESSÁRIAS

```json
{
  "@nestjs/schedule": "^4.0.0",
  "node-cron": "^3.0.2",
  "bull": "^4.10.4",
  "@nestjs/bull": "^10.0.1"
}
```

## CONFIGURAÇÕES NECESSÁRIAS

### Environment Variables
```env
# Scheduler
SUBSCRIPTION_SCHEDULER_ENABLED=true
BILLING_SCHEDULER_ENABLED=true
QI_DAY_DUE=3

# Redis para Jobs
REDIS_URL=redis://localhost:6379
BULL_REDIS_HOST=localhost
BULL_REDIS_PORT=6379
```

## ESTIMATIVA DE ESFORÇO

- **Prioridade Crítica**: 5-6 dias (40-48 horas)
- **Prioridade Alta**: 3-4 dias (24-32 horas)
- **Total**: 8-10 dias (64-80 horas)

## RISCOS

### Alto Risco
- **Migração de dados**: Assinaturas ativas podem ser afetadas
- **Scheduler**: Falhas podem deixar usuários sem serviço

### Mitigações
- Implementar com feature flags
- Testes extensivos em ambiente de staging
- Rollback plan preparado
- Monitoramento em tempo real

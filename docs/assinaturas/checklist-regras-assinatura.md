# Checklist de Verificação das Regras de Assinatura

## PRIMEIRA ASSINATURA

### CARTÃO

#### Mensal
- [ ] ❌ **Não pode parcelar**: ✅ IMPLEMENTADO - Código força `installments = 1` para planos mensais
- [ ] ❌ **Valor total do plano**: ✅ IMPLEMENTADO - Usa valor total sem desconto
- [ ] ❌ **Inicia no dia da assinatura e vence 30 dias após**: ✅ IMPLEMENTADO - Lógica de datas implementada
- [ ] ❌ **Cobrança 3 dias antes do vencimento**: ❌ NÃO IMPLEMENTADO - Não há lógica específica para cobrança 3 dias antes

#### Anual
- [ ] ❌ **Inicia no dia da assinatura e vence 365 dias após**: ✅ IMPLEMENTADO - Lógica de datas implementada
- [ ] ❌ **Pode parcelar em até 12 vezes**: ✅ IMPLEMENTADO - Config permite até 12 parcelas
- [ ] ❌ **Se parcelado, pode escolher a data de vencimento**: ✅ IMPLEMENTADO - Interface permite seleção de data
- [ ] ❌ **Parcelado - Se escolher outra data, paga valor proporcional**: ✅ IMPLEMENTADO - Função `calculateYearlyInstallmentsAmount`
- [ ] ❌ **Tem até 3 dias para pagar primeira parcela**: ✅ IMPLEMENTADO - `QI_DAY_DUE` configurado para 3 dias
- [ ] ❌ **Demais vencem no dia escolhido**: ✅ IMPLEMENTADO - Lógica de `calculateInstallmentDueDate`
- [ ] ❌ **Se não escolher, paga valor total**: ✅ IMPLEMENTADO - Default para valor total

### BOLETO (Somente PJ)

#### Validação PJ
- [ ] ❌ **Boleto apenas para PJ**: ✅ IMPLEMENTADO - Interface mostra boleto apenas se `isCompany = true`

#### Mensal
- [ ] ❌ **Não pode parcelar**: ✅ IMPLEMENTADO - Força `installments = 1` para mensais
- [ ] ❌ **Valor total do plano**: ✅ IMPLEMENTADO
- [ ] ❌ **Inicia no dia da assinatura e vence 30 dias após**: ✅ IMPLEMENTADO
- [ ] ❌ **Cobrança 3 dias antes do vencimento**: ❌ NÃO IMPLEMENTADO

#### Anual
- [ ] ❌ **Inicia no dia da assinatura e vence 365 dias após**: ✅ IMPLEMENTADO
- [ ] ❌ **Pode parcelar em até 12 vezes**: ✅ IMPLEMENTADO
- [ ] ❌ **Se parcelado, pode escolher data de vencimento**: ✅ IMPLEMENTADO
- [ ] ❌ **Parcelado - valor proporcional se escolher outra data**: ✅ IMPLEMENTADO
- [ ] ❌ **3 dias para pagar primeira parcela**: ✅ IMPLEMENTADO
- [ ] ❌ **Demais vencem no dia escolhido**: ✅ IMPLEMENTADO
- [ ] ❌ **Se não escolher, paga valor total**: ✅ IMPLEMENTADO
- [ ] ❌ **Não parcelado - cobrança 3 dias antes**: ❌ NÃO IMPLEMENTADO

### PIX

#### Mensal
- [ ] ❌ **Não pode parcelar**: ✅ IMPLEMENTADO - Força `installments = 1`
- [ ] ❌ **Valor total do plano**: ✅ IMPLEMENTADO
- [ ] ❌ **Inicia no dia da assinatura e vence 30 dias após**: ✅ IMPLEMENTADO
- [ ] ❌ **Cobrança 3 dias antes do vencimento**: ❌ NÃO IMPLEMENTADO

#### Anual
- [ ] ❌ **Inicia no dia da assinatura e vence 365 dias após**: ✅ IMPLEMENTADO
- [ ] ❌ **Pode parcelar em até 12 vezes**: ✅ IMPLEMENTADO
- [ ] ❌ **Se parcelado, pode escolher data de vencimento**: ✅ IMPLEMENTADO
- [ ] ❌ **Parcelado - valor proporcional se escolher outra data**: ✅ IMPLEMENTADO
- [ ] ❌ **3 dias para pagar primeira parcela**: ✅ IMPLEMENTADO
- [ ] ❌ **Demais vencem no dia escolhido**: ✅ IMPLEMENTADO
- [ ] ❌ **Se não escolher, paga valor total**: ✅ IMPLEMENTADO
- [ ] ❌ **Não parcelado - cobrança 3 dias antes**: ❌ NÃO IMPLEMENTADO

## UPGRADE

### ANUAL PARA ANUAL
- [ ] ❌ **Recebe crédito do montante pago não consumido**: ✅ IMPLEMENTADO - `calculateSubscriptionCredit`
- [ ] ❌ **Valor proporcional ao tempo restante**: ✅ IMPLEMENTADO - Lógica de cálculo proporcional
- [ ] ❌ **Nova assinatura com mesma data fim**: ✅ IMPLEMENTADO - `createSubscriptionUpgradePlan`
- [ ] ❌ **Se diferença positiva - pode parcelar até 12x**: ✅ IMPLEMENTADO
- [ ] ❌ **Parcelado - valor proporcional se mudar vencimento**: ✅ IMPLEMENTADO
- [ ] ❌ **Parcelado - valor total se manter vencimento**: ✅ IMPLEMENTADO
- [ ] ❌ **3 dias para pagar primeira parcela**: ✅ IMPLEMENTADO
- [ ] ❌ **Upgrade aplicado ao pagar primeira parcela**: ❌ PARCIALMENTE - Precisa verificar webhook
- [ ] ❌ **Não parcelado - 3 dias para pagar**: ✅ IMPLEMENTADO
- [ ] ❌ **Não parcelado - upgrade imediato ao pagar**: ❌ PARCIALMENTE - Precisa verificar webhook
- [ ] ❌ **Se diferença negativa - nova assinatura agendada**: ✅ IMPLEMENTADO - `scheduleUpgradeSubscription`
- [ ] ❌ **Atual cancelada no fim do período**: ✅ IMPLEMENTADO
- [ ] ❌ **Sem cobrança no momento do upgrade**: ✅ IMPLEMENTADO
- [ ] ❌ **Upgrade aplicado na ativação da nova**: ❌ PARCIALMENTE - Precisa verificar scheduler

### MENSAL PARA MENSAL
- [ ] ❌ **Recebe crédito do montante pago não consumido**: ✅ IMPLEMENTADO
- [ ] ❌ **Valor proporcional ao tempo restante**: ✅ IMPLEMENTADO
- [ ] ❌ **Nova assinatura com mesma data fim**: ✅ IMPLEMENTADO
- [ ] ❌ **Se diferença positiva - paga valor proporcional**: ✅ IMPLEMENTADO
- [ ] ❌ **3 dias para pagar**: ✅ IMPLEMENTADO
- [ ] ❌ **Upgrade aplicado imediatamente ao pagar**: ❌ PARCIALMENTE - Precisa verificar webhook
- [ ] ❌ **Se diferença negativa - nova assinatura agendada**: ✅ IMPLEMENTADO
- [ ] ❌ **Atual cancelada no fim do período**: ✅ IMPLEMENTADO
- [ ] ❌ **Sem cobrança no momento**: ✅ IMPLEMENTADO
- [ ] ❌ **Upgrade aplicado na ativação**: ❌ PARCIALMENTE - Precisa verificar scheduler

### MENSAL PARA ANUAL
- [ ] ❌ **Agenda atual para cancelar no fim**: ✅ IMPLEMENTADO - `scheduleUpgradeSubscription`
- [ ] ❌ **Nova assinatura anual agendada**: ✅ IMPLEMENTADO
- [ ] ❌ **Upgrade aplicado na ativação**: ❌ PARCIALMENTE - Precisa verificar scheduler
- [ ] ❌ **Pode parcelar até 12x**: ✅ IMPLEMENTADO
- [ ] ❌ **Data de cobrança igual à atual**: ✅ IMPLEMENTADO
- [ ] ❌ **Valor total sem desconto**: ✅ IMPLEMENTADO
- [ ] ❌ **Sem cobrança no momento**: ✅ IMPLEMENTADO

### ANUAL PARA MENSAL
- [ ] ❌ **Agenda atual para cancelar no fim**: ✅ IMPLEMENTADO
- [ ] ❌ **Nova assinatura mensal agendada**: ✅ IMPLEMENTADO
- [ ] ❌ **Upgrade aplicado na ativação**: ❌ PARCIALMENTE - Precisa verificar scheduler
- [ ] ❌ **Paga valor total do plano**: ✅ IMPLEMENTADO
- [ ] ❌ **3 dias para pagar**: ✅ IMPLEMENTADO
- [ ] ❌ **Upgrade aplicado imediatamente ao pagar**: ❌ INCONSISTENTE - Regra conflitante

## DOWNGRADE
- [ ] ❌ **Agenda atual para cancelar no fim**: ✅ IMPLEMENTADO
- [ ] ❌ **Nova assinatura agendada**: ✅ IMPLEMENTADO
- [ ] ❌ **Downgrade aplicado na ativação**: ❌ PARCIALMENTE - Precisa verificar scheduler
- [ ] ❌ **Paga valor total**: ✅ IMPLEMENTADO
- [ ] ❌ **3 dias para pagar**: ✅ IMPLEMENTADO
- [ ] ❌ **Downgrade aplicado imediatamente ao pagar**: ❌ INCONSISTENTE - Regra conflitante
- [ ] ❌ **Nenhuma cobrança no momento**: ✅ IMPLEMENTADO

## RESUMO DE PROBLEMAS IDENTIFICADOS

### ❌ CRÍTICOS
1. **Cobrança 3 dias antes do vencimento**: Não implementada para renovações automáticas
2. **Aplicação imediata vs agendada**: Inconsistências nas regras de ANUAL PARA MENSAL e DOWNGRADE
3. **Scheduler de ativação**: Não verificado se existe sistema para ativar assinaturas agendadas
4. **Webhooks de upgrade**: Não verificado se aplicam mudanças imediatamente

### ⚠️ ATENÇÃO
1. **Validação de regras no frontend**: Falta validação mais rigorosa das regras de negócio
2. **Documentação**: Regras conflitantes entre documentos
3. **Testes**: Não foram encontrados testes específicos para as regras de negócio

### ✅ IMPLEMENTADOS CORRETAMENTE
1. Validação de boleto apenas para PJ
2. Parcelamento apenas para planos anuais
3. Cálculo de valores proporcionais
4. Cálculo de créditos de upgrade
5. Agendamento de assinaturas futuras
6. Interface de seleção de datas de vencimento

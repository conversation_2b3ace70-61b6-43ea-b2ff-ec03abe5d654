# Checklist Detalhado - Frontend e Backend

## FRONTEND

### ✅ IMPLEMENTADO CORRETAMENTE

#### Validação de Boleto para PJ
- **Arquivo**: `frontend/src/components/checkout/PaymentFields.tsx:239-255`
- **Status**: ✅ CORRETO
- **Implementação**: Mostra opção de boleto apenas quando `isCompany = true`
- **Código**: 
```tsx
{isCompany && (
  <RadioGroupItem value={PaymentMethod.BOLETO} />
)}
```

#### Parcelamento Apenas para Anuais
- **Arquivo**: `frontend/src/components/checkout/PaymentFields.tsx:352-370`
- **Status**: ✅ CORRETO  
- **Implementação**: Campo de parcelas desabilitado quando `!isYearly`
- **Código**:
```tsx
disabled={!isYearly}
```

#### Seleção de Data de Vencimento
- **Arquivo**: `frontend/src/components/checkout/PaymentFields.tsx:469-489`
- **Status**: ✅ CORRETO
- **Implementação**: Permite escolher data apenas quando parcelado e não é cartão
- **Código**:
```tsx
{Number(selectedInstallments) > 1 && paymentMethod !== PaymentMethod.CREDIT_CARD && (
  <Select>...</Select>
)}
```

#### Configuração de Parcelas
- **Arquivo**: `frontend/src/config/payment.ts:1-2`
- **Status**: ✅ CORRETO
- **Implementação**: Define parcelas aceitas [1, 12]
- **Código**:
```typescript
export const installments = [1, 12]
export const paymentDays = [5, 10, 15, 20, 25]
```

### ❌ PROBLEMAS IDENTIFICADOS

#### Falta Validação de Regras de Negócio
- **Problema**: Não valida regras específicas antes do envio
- **Impacto**: Usuário pode tentar ações inválidas
- **Solução**: Implementar `useSubscriptionValidation` hook

#### Interface Não Explica Regras
- **Problema**: Usuário não entende quando upgrade será aplicado
- **Impacto**: Confusão sobre cobrança e ativação
- **Solução**: Componente `SubscriptionRulesInfo`

#### Cálculo de Valores Não Transparente
- **Arquivo**: `frontend/src/lib/plan.utils.ts:201-219`
- **Problema**: Lógica complexa sem explicação ao usuário
- **Impacto**: Usuário não entende valores mostrados
- **Solução**: Componente explicativo dos cálculos

### 🔧 MODIFICAÇÕES NECESSÁRIAS NO FRONTEND

#### 1. Criar Hook de Validação
```typescript
// frontend/src/hooks/useSubscriptionValidation.ts
export function useSubscriptionValidation() {
  const validateSubscriptionRules = (formData: CheckoutFormData) => {
    const errors: ValidationError[] = [];
    
    // Validar boleto apenas para PJ
    if (formData.paymentMethod === PaymentMethod.BOLETO && !formData.isCompany) {
      errors.push({
        field: 'paymentMethod',
        message: 'Boleto disponível apenas para pessoa jurídica'
      });
    }
    
    // Validar parcelamento apenas para anuais
    if (formData.installments > 1 && !formData.isYearly) {
      errors.push({
        field: 'installments', 
        message: 'Parcelamento disponível apenas para planos anuais'
      });
    }
    
    return { isValid: errors.length === 0, errors };
  };
  
  return { validateSubscriptionRules };
}
```

#### 2. Integrar Validação no CheckoutContext
```typescript
// frontend/src/contexts/checkout/CheckoutContext.tsx
const { validateSubscriptionRules } = useSubscriptionValidation();

const handleSubmit = (data: CheckoutFormData) => {
  const validation = validateSubscriptionRules(data);
  
  if (!validation.isValid) {
    // Mostrar erros de validação
    validation.errors.forEach(error => {
      form.setError(error.field, { message: error.message });
    });
    return;
  }
  
  // Prosseguir com envio
  onSubmit(data);
};
```

#### 3. Componente de Explicação das Regras
```typescript
// frontend/src/components/checkout/SubscriptionRulesInfo.tsx
export function SubscriptionRulesInfo({ 
  paymentMethod, 
  isYearly, 
  isUpgrade,
  upgradeType 
}: Props) {
  const rules = useMemo(() => {
    if (isUpgrade) {
      return getUpgradeRules(upgradeType);
    }
    return getNewSubscriptionRules(paymentMethod, isYearly);
  }, [paymentMethod, isYearly, isUpgrade, upgradeType]);
  
  return (
    <Alert className="mb-4">
      <Info className="h-4 w-4" />
      <AlertTitle>Como funciona sua assinatura</AlertTitle>
      <AlertDescription>
        <ul className="list-disc list-inside space-y-1 mt-2">
          {rules.map((rule, index) => (
            <li key={index} className="text-sm">{rule}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}
```

## BACKEND

### ✅ IMPLEMENTADO CORRETAMENTE

#### Força Parcela Única para Mensais
- **Arquivo**: `backend/src/modules/payment/payment.service.ts:179-182`
- **Status**: ✅ CORRETO
- **Implementação**: Força `installments = 1` para planos mensais
- **Código**:
```typescript
if (!isYearly) {
  installments = 1;
}
```

#### Cálculo de Valores Proporcionais
- **Arquivo**: `backend/src/modules/payment/payment.service.ts:192-200`
- **Status**: ✅ CORRETO
- **Implementação**: Usa `calculateYearlyInstallmentsAmount`
- **Função**: Calcula primeira parcela proporcional e demais parcelas

#### Validação de Tipo de Cliente
- **Arquivo**: `backend/src/modules/payment/dto/create-payment.dto.ts:51-53`
- **Status**: ✅ CORRETO
- **Implementação**: Valida CNPJ apenas se `isCompany = true`
- **Código**:
```typescript
@ValidateIf((o: CreatePaymentDto) => o.isCompany === true)
@IsString()
companyCnpj?: string;
```

#### Cálculo de Crédito de Upgrade
- **Arquivo**: `backend/src/modules/payment/utils/upgrade-calculator.ts:134-152`
- **Status**: ✅ CORRETO
- **Implementação**: Calcula crédito baseado em faturas pagas
- **Função**: `calculateSubscriptionCredit`

#### Agendamento de Assinaturas
- **Arquivo**: `backend/src/modules/subscription/subscription.service.ts:89-105`
- **Status**: ✅ CORRETO
- **Implementação**: `scheduleUpgradeSubscription` agenda nova assinatura
- **Funcionalidade**: Cancela atual e cria futura

### ❌ PROBLEMAS IDENTIFICADOS

#### Cobrança 3 Dias Antes Não Implementada
- **Problema**: Não existe sistema para cobrança automática 3 dias antes
- **Impacto**: Renovações podem falhar por falta de saldo
- **Arquivos Afetados**: Todos os serviços de pagamento
- **Solução**: Implementar `BillingSchedulerService`

#### Aplicação Imediata vs Agendada Inconsistente
- **Problema**: Regras conflitantes para ANUAL→MENSAL e DOWNGRADE
- **Arquivo**: `backend/src/modules/upgrade/upgrade.service.ts:314-320`
- **Impacto**: Comportamento imprevisível para usuário
- **Solução**: Implementar `UpgradeRulesService` com regras claras

#### Webhook Não Aplica Upgrade Imediato
- **Problema**: Não verifica se deve aplicar upgrade ao receber pagamento
- **Impacto**: Upgrades podem não ser aplicados imediatamente
- **Solução**: Melhorar `webhook.service.ts`

#### Scheduler de Ativação Não Existe
- **Problema**: Assinaturas agendadas podem não ser ativadas
- **Impacto**: Usuários ficam sem serviço após upgrade agendado
- **Solução**: Implementar `SubscriptionSchedulerService`

### 🔧 MODIFICAÇÕES NECESSÁRIAS NO BACKEND

#### 1. Sistema de Cobrança 3 Dias Antes
```typescript
// backend/src/modules/billing/billing-scheduler.service.ts
@Injectable()
export class BillingSchedulerService {
  @Cron('0 6 * * *') // Todo dia às 6h
  async processPreBillingCharges() {
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
    
    const subscriptionsToCharge = await this.subscriptionService
      .getSubscriptionsWithBillingDate(threeDaysFromNow);
    
    for (const subscription of subscriptionsToCharge) {
      await this.processPreBillingCharge(subscription);
    }
  }
  
  private async processPreBillingCharge(subscription: QISubscription) {
    // Gerar cobrança 3 dias antes
    // Enviar notificação ao usuário
    // Agendar retry se falhar
  }
}
```

#### 2. Regras Consistentes de Upgrade
```typescript
// backend/src/modules/upgrade/upgrade-rules.service.ts
@Injectable()
export class UpgradeRulesService {
  determineUpgradeApplication(
    current: QISubscription,
    newPlan: any
  ): UpgradeApplicationRule {
    
    const isChangingCycle = current.billingInterval !== 
      (newPlan.isYearly ? QIBillingInterval.YEARLY : QIBillingInterval.MONTHLY);
    
    const isUpgrade = newPlan.totalPrice > this.getCurrentPlanPrice(current);
    
    // Regras claras e consistentes
    if (isChangingCycle) {
      return {
        type: 'SCHEDULED',
        reason: 'BILLING_CYCLE_CHANGE',
        activationDate: current.currentPeriodEnd.toDate()
      };
    }
    
    if (isUpgrade) {
      return {
        type: 'IMMEDIATE',
        reason: 'SAME_CYCLE_UPGRADE',
        activationDate: new Date()
      };
    }
    
    return {
      type: 'SCHEDULED', 
      reason: 'DOWNGRADE',
      activationDate: current.currentPeriodEnd.toDate()
    };
  }
}
```

#### 3. Scheduler de Ativação
```typescript
// backend/src/modules/subscription/subscription-scheduler.service.ts
@Injectable()
export class SubscriptionSchedulerService {
  @Cron('0 0 * * *') // Todo dia à meia-noite
  async processScheduledActions() {
    const today = new Date();
    
    // Ativar assinaturas agendadas
    await this.activateScheduledSubscriptions(today);
    
    // Cancelar assinaturas agendadas
    await this.cancelScheduledSubscriptions(today);
    
    // Expirar assinaturas vencidas
    await this.expireOverdueSubscriptions(today);
  }
  
  private async activateScheduledSubscriptions(date: Date) {
    const subscriptions = await this.subscriptionService
      .getSubscriptionsToActivate(date);
    
    for (const subscription of subscriptions) {
      await this.activateSubscription(subscription);
    }
  }
}
```

## RESUMO EXECUTIVO

### ✅ FUNCIONALIDADES CORRETAS (70%)
- Validação de boleto para PJ
- Parcelamento apenas para anuais  
- Cálculo de valores proporcionais
- Cálculo de créditos de upgrade
- Agendamento básico de assinaturas
- Interface de seleção de datas

### ❌ FUNCIONALIDADES COM PROBLEMAS (30%)
- Cobrança 3 dias antes do vencimento
- Aplicação imediata vs agendada inconsistente
- Sistema de scheduler para ativação
- Webhooks de upgrade imediato
- Validações rigorosas no frontend
- Explicação das regras ao usuário

### 🎯 PRÓXIMOS PASSOS
1. **Implementar sistema de scheduler** (crítico)
2. **Corrigir regras de aplicação** (crítico)  
3. **Melhorar webhooks** (importante)
4. **Adicionar validações frontend** (importante)
5. **Implementar cobrança 3 dias antes** (desejável)

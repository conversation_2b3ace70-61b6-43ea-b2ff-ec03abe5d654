# Modificações Necessárias - Frontend

## PRIORIDADE ALTA

### 1. Hook de Validação de Regras de Negócio

**Arquivo**: `frontend/src/hooks/useSubscriptionValidation.ts`

```typescript
import { PaymentMethod } from '@/types/payment';
import { Plan } from '@/types/plan';

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface UpgradeValidationResult extends ValidationResult {
  upgradeType: 'IMMEDIATE' | 'SCHEDULED';
  applicationType: 'MONTHLY_TO_MONTHLY' | 'YEARLY_TO_YEARLY' | 'MONTHLY_TO_YEARLY' | 'YEARLY_TO_MONTHLY';
  message: string;
}

export function useSubscriptionValidation() {
  
  const validatePaymentMethod = (
    paymentMethod: PaymentMethod,
    isCompany: boolean,
    isYearly: boolean,
    installments: number
  ): ValidationResult => {
    const errors: ValidationError[] = [];
    
    // Regra: Boleto apenas para PJ
    if (paymentMethod === PaymentMethod.BOLETO && !isCompany) {
      errors.push({
        field: 'paymentMethod',
        message: 'Boleto bancário disponível apenas para pessoa jurídica'
      });
    }
    
    // Regra: Parcelamento apenas para anuais
    if (installments > 1 && !isYearly) {
      errors.push({
        field: 'installments',
        message: 'Parcelamento disponível apenas para planos anuais'
      });
    }
    
    // Regra: Máximo 12 parcelas
    if (installments > 12) {
      errors.push({
        field: 'installments',
        message: 'Máximo de 12 parcelas permitidas'
      });
    }
    
    // Regra: Mínimo 1 parcela
    if (installments < 1) {
      errors.push({
        field: 'installments',
        message: 'Mínimo de 1 parcela obrigatória'
      });
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };
  
  const validateUpgrade = (
    currentPlan: Plan,
    newPlan: Plan
  ): UpgradeValidationResult => {
    const errors: ValidationError[] = [];
    
    // Determinar tipo de mudança
    const isChangingBillingCycle = currentPlan.isYearly !== newPlan.isYearly;
    const isUpgrade = newPlan.totalPrice > currentPlan.totalPrice;
    
    let applicationType: UpgradeValidationResult['applicationType'];
    let upgradeType: 'IMMEDIATE' | 'SCHEDULED';
    let message: string;
    
    if (!currentPlan.isYearly && !newPlan.isYearly) {
      applicationType = 'MONTHLY_TO_MONTHLY';
      upgradeType = 'IMMEDIATE';
      message = 'Upgrade aplicado imediatamente após o pagamento';
    } else if (currentPlan.isYearly && newPlan.isYearly) {
      applicationType = 'YEARLY_TO_YEARLY';
      upgradeType = 'IMMEDIATE';
      message = 'Upgrade aplicado imediatamente após o pagamento';
    } else if (!currentPlan.isYearly && newPlan.isYearly) {
      applicationType = 'MONTHLY_TO_YEARLY';
      upgradeType = 'SCHEDULED';
      message = 'Upgrade será aplicado no fim do período atual';
    } else {
      applicationType = 'YEARLY_TO_MONTHLY';
      upgradeType = 'SCHEDULED';
      message = 'Upgrade será aplicado no fim do período atual';
    }
    
    // Validações específicas
    if (currentPlan.id === newPlan.id && currentPlan.totalPrice === newPlan.totalPrice) {
      errors.push({
        field: 'plan',
        message: 'Selecione um plano diferente do atual'
      });
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      upgradeType,
      applicationType,
      message
    };
  };
  
  return {
    validatePaymentMethod,
    validateUpgrade
  };
}
```

### 2. Componente de Explicação das Regras

**Arquivo**: `frontend/src/components/checkout/SubscriptionRulesInfo.tsx`

```typescript
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Info } from 'lucide-react';
import { PaymentMethod } from '@/types/payment';

interface SubscriptionRulesInfoProps {
  paymentMethod: PaymentMethod;
  isYearly: boolean;
  installments: number;
  isUpgrade?: boolean;
  upgradeType?: 'IMMEDIATE' | 'SCHEDULED';
  applicationType?: 'MONTHLY_TO_MONTHLY' | 'YEARLY_TO_YEARLY' | 'MONTHLY_TO_YEARLY' | 'YEARLY_TO_MONTHLY';
}

export function SubscriptionRulesInfo({
  paymentMethod,
  isYearly,
  installments,
  isUpgrade = false,
  upgradeType,
  applicationType
}: SubscriptionRulesInfoProps) {
  
  const getNewSubscriptionRules = (): string[] => {
    const rules: string[] = [];
    
    if (isYearly) {
      rules.push('📅 Assinatura anual com duração de 365 dias');
      
      if (installments > 1) {
        if (paymentMethod === PaymentMethod.CREDIT_CARD) {
          rules.push('💳 Primeira parcela debitada hoje, demais no mesmo dia dos próximos meses');
        } else {
          rules.push('📊 Primeira parcela com valor proporcional ao período');
          rules.push('⏰ Você tem até 3 dias para pagar cada parcela');
          rules.push('📆 Demais parcelas vencem no dia escolhido de cada mês');
        }
      } else {
        rules.push('💰 Pagamento único do valor total');
        if (paymentMethod !== PaymentMethod.CREDIT_CARD) {
          rules.push('🔔 Cobrança será feita 3 dias antes do vencimento');
        }
      }
    } else {
      rules.push('📅 Assinatura mensal com renovação automática a cada 30 dias');
      rules.push('🚫 Não é possível parcelar planos mensais');
      if (paymentMethod !== PaymentMethod.CREDIT_CARD) {
        rules.push('🔔 Cobrança será feita 3 dias antes do vencimento');
      }
    }
    
    // Regras específicas por método
    if (paymentMethod === PaymentMethod.BOLETO) {
      rules.push('🏢 Boleto bancário disponível apenas para pessoa jurídica');
    }
    
    return rules;
  };
  
  const getUpgradeRules = (): string[] => {
    const rules: string[] = [];
    
    switch (applicationType) {
      case 'MONTHLY_TO_MONTHLY':
        rules.push('⚡ Upgrade aplicado imediatamente após o pagamento');
        rules.push('💰 Você recebe crédito proporcional do plano atual');
        rules.push('📊 Paga apenas a diferença até o fim do período atual');
        rules.push('🔄 Nova assinatura termina na mesma data da atual');
        break;
        
      case 'YEARLY_TO_YEARLY':
        rules.push('⚡ Upgrade aplicado imediatamente após o pagamento');
        rules.push('💰 Você recebe crédito proporcional do plano atual');
        rules.push('📊 Nova assinatura termina na mesma data da atual');
        if (installments > 1) {
          rules.push('💳 Pode parcelar a diferença em até 12 vezes');
        }
        break;
        
      case 'MONTHLY_TO_YEARLY':
        rules.push('⏳ Upgrade será aplicado no fim do período atual');
        rules.push('📅 Plano anual inicia quando o mensal terminar');
        rules.push('🆓 Sem cobrança imediata - pagamento na data de ativação');
        rules.push('💳 Pode parcelar o plano anual em até 12 vezes');
        break;
        
      case 'YEARLY_TO_MONTHLY':
        rules.push('⏳ Upgrade será aplicado no fim do período atual');
        rules.push('📅 Plano mensal inicia quando o anual terminar');
        rules.push('🆓 Sem cobrança imediata - pagamento na data de ativação');
        break;
    }
    
    return rules;
  };
  
  const rules = isUpgrade ? getUpgradeRules() : getNewSubscriptionRules();
  const title = isUpgrade ? 'Como funciona seu upgrade' : 'Como funciona sua assinatura';
  
  return (
    <Alert className="mb-4 border-blue-200 bg-blue-50">
      <Info className="h-4 w-4 text-blue-600" />
      <AlertTitle className="text-blue-900">{title}</AlertTitle>
      <AlertDescription>
        <ul className="space-y-1 mt-2">
          {rules.map((rule, index) => (
            <li key={index} className="text-sm text-blue-800 flex items-start gap-2">
              <span className="text-blue-600 font-medium">{rule.split(' ')[0]}</span>
              <span>{rule.split(' ').slice(1).join(' ')}</span>
            </li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}
```

### 3. Componente de Breakdown de Valores

**Arquivo**: `frontend/src/components/checkout/PriceBreakdown.tsx`

```typescript
import { Card } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';

interface PriceBreakdownProps {
  originalPrice: number;
  credit?: number;
  discount?: number;
  finalPrice: number;
  isUpgrade?: boolean;
  installments?: number;
  nextBillingPrice?: number;
}

export function PriceBreakdown({
  originalPrice,
  credit = 0,
  discount = 0,
  finalPrice,
  isUpgrade = false,
  installments = 1,
  nextBillingPrice
}: PriceBreakdownProps) {
  
  const installmentValue = installments > 1 ? finalPrice / installments : finalPrice;
  
  return (
    <Card className="p-4 bg-gray-50 border-gray-200">
      <h4 className="font-medium mb-3 text-gray-900">
        {isUpgrade ? 'Resumo do upgrade' : 'Resumo do pagamento'}
      </h4>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">
            {isUpgrade ? 'Valor do novo plano:' : 'Valor do plano:'}
          </span>
          <span className="font-medium">{formatCurrency(originalPrice)}</span>
        </div>
        
        {credit > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Crédito do plano atual:</span>
            <span>-{formatCurrency(credit)}</span>
          </div>
        )}
        
        {discount > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Desconto aplicado:</span>
            <span>-{formatCurrency(discount)}</span>
          </div>
        )}
        
        <div className="border-t pt-2 mt-2">
          <div className="flex justify-between font-medium text-lg">
            <span>Total a pagar agora:</span>
            <span className="text-green-600">{formatCurrency(finalPrice)}</span>
          </div>
          
          {installments > 1 && (
            <div className="flex justify-between text-sm text-gray-600 mt-1">
              <span>Em {installments}x de:</span>
              <span>{formatCurrency(installmentValue)} sem juros</span>
            </div>
          )}
        </div>
        
        {nextBillingPrice && nextBillingPrice !== finalPrice && (
          <div className="bg-blue-50 p-3 rounded-lg mt-3 border border-blue-200">
            <div className="flex justify-between text-sm">
              <span className="text-blue-700">Próximas cobranças:</span>
              <span className="font-medium text-blue-900">{formatCurrency(nextBillingPrice)}</span>
            </div>
            <p className="text-xs text-blue-600 mt-1">
              Valor das próximas renovações sem desconto
            </p>
          </div>
        )}
      </div>
    </Card>
  );
}
```

## PRIORIDADE MÉDIA

### 4. Integração no CheckoutContext

**Modificar**: `frontend/src/contexts/checkout/CheckoutContext.tsx`

```typescript
// Adicionar no início do arquivo
import { useSubscriptionValidation } from '@/hooks/useSubscriptionValidation';

// Dentro do CheckoutProvider
const { validatePaymentMethod, validateUpgrade } = useSubscriptionValidation();

// Adicionar função de validação
const validateForm = (data: CheckoutFormData) => {
  // Validar método de pagamento
  const paymentValidation = validatePaymentMethod(
    data.paymentMethod,
    data.isCompany,
    isYearly,
    Number(data.installments)
  );
  
  if (!paymentValidation.isValid) {
    paymentValidation.errors.forEach(error => {
      form.setError(error.field as any, { message: error.message });
    });
    return false;
  }
  
  // Validar upgrade se aplicável
  if (isUpgrade && selectedPlan && currentPlan) {
    const upgradeValidation = validateUpgrade(currentPlan, selectedPlan);
    
    if (!upgradeValidation.isValid) {
      upgradeValidation.errors.forEach(error => {
        form.setError(error.field as any, { message: error.message });
      });
      return false;
    }
  }
  
  return true;
};

// Modificar handleSubmit
const handleSubmit = (data: CheckoutFormData) => {
  if (!validateForm(data)) {
    return;
  }
  
  // Prosseguir com envio
  onSubmit(data);
};
```

### 5. Modificar PaymentFields

**Modificar**: `frontend/src/components/checkout/PaymentFields.tsx`

```typescript
// Adicionar imports
import { SubscriptionRulesInfo } from './SubscriptionRulesInfo';
import { PriceBreakdown } from './PriceBreakdown';
import { useSubscriptionValidation } from '@/hooks/useSubscriptionValidation';

// Dentro do componente
const { validatePaymentMethod, validateUpgrade } = useSubscriptionValidation();

// Adicionar validação em tempo real
useEffect(() => {
  if (paymentMethod && typeof isCompany !== 'undefined') {
    const validation = validatePaymentMethod(
      paymentMethod,
      isCompany,
      isYearly,
      Number(selectedInstallments)
    );
    
    if (!validation.isValid) {
      validation.errors.forEach(error => {
        if (error.field === 'paymentMethod') {
          // Mostrar erro específico
          toast({
            title: "Método de pagamento inválido",
            description: error.message,
            variant: "destructive",
          });
        }
      });
    }
  }
}, [paymentMethod, isCompany, isYearly, selectedInstallments]);

// Adicionar componentes no JSX (antes do OrderSummary)
return (
  <div className="space-y-6">
    {/* Componente de regras */}
    <SubscriptionRulesInfo
      paymentMethod={paymentMethod}
      isYearly={isYearly}
      installments={Number(selectedInstallments)}
      isUpgrade={isUpgrade}
      upgradeType={upgradeValidation?.upgradeType}
      applicationType={upgradeValidation?.applicationType}
    />
    
    {/* Breakdown de valores */}
    <PriceBreakdown
      originalPrice={selectedPlan?.totalPrice || 0}
      credit={currentPlan?.credit || 0}
      finalPrice={total}
      isUpgrade={isUpgrade}
      installments={Number(selectedInstallments)}
      nextBillingPrice={selectedPlan?.basePrice}
    />
    
    {/* Resto do componente... */}
  </div>
);
```

## PRIORIDADE BAIXA

### 6. Utilitários de Regras

**Arquivo**: `frontend/src/utils/subscription-rules.utils.ts`

```typescript
import { PaymentMethod } from '@/types/payment';

export const getPaymentMethodRules = (method: PaymentMethod, isYearly: boolean) => {
  const rules = {
    [PaymentMethod.CREDIT_CARD]: {
      canInstall: isYearly,
      maxInstallments: 12,
      billingDayFixed: true,
      preBilling: false,
      companyOnly: false
    },
    [PaymentMethod.BOLETO]: {
      canInstall: isYearly,
      maxInstallments: 12,
      billingDayFixed: false,
      preBilling: true,
      companyOnly: true
    },
    [PaymentMethod.PIX]: {
      canInstall: isYearly,
      maxInstallments: 12,
      billingDayFixed: false,
      preBilling: true,
      companyOnly: false
    }
  };
  
  return rules[method];
};

export const getUpgradeTypeMessage = (
  currentIsYearly: boolean,
  newIsYearly: boolean,
  isUpgrade: boolean
): string => {
  if (currentIsYearly === newIsYearly) {
    return isUpgrade 
      ? 'Upgrade aplicado imediatamente após o pagamento'
      : 'Downgrade aplicado no fim do período atual';
  }
  
  return 'Mudança será aplicada no fim do período atual';
};
```

### 7. Testes

**Arquivo**: `frontend/src/components/checkout/__tests__/SubscriptionRulesInfo.test.tsx`

```typescript
import { render, screen } from '@testing-library/react';
import { SubscriptionRulesInfo } from '../SubscriptionRulesInfo';
import { PaymentMethod } from '@/types/payment';

describe('SubscriptionRulesInfo', () => {
  it('should show boleto company restriction', () => {
    render(
      <SubscriptionRulesInfo
        paymentMethod={PaymentMethod.BOLETO}
        isYearly={true}
        installments={1}
      />
    );
    
    expect(screen.getByText(/pessoa jurídica/i)).toBeInTheDocument();
  });
  
  it('should show upgrade immediate message', () => {
    render(
      <SubscriptionRulesInfo
        paymentMethod={PaymentMethod.CREDIT_CARD}
        isYearly={true}
        installments={1}
        isUpgrade={true}
        upgradeType="IMMEDIATE"
        applicationType="YEARLY_TO_YEARLY"
      />
    );
    
    expect(screen.getByText(/imediatamente após o pagamento/i)).toBeInTheDocument();
  });
});
```

## RESUMO DAS MODIFICAÇÕES

### Arquivos a Criar (5)
1. `frontend/src/hooks/useSubscriptionValidation.ts`
2. `frontend/src/components/checkout/SubscriptionRulesInfo.tsx`
3. `frontend/src/components/checkout/PriceBreakdown.tsx`
4. `frontend/src/utils/subscription-rules.utils.ts`
5. `frontend/src/components/checkout/__tests__/SubscriptionRulesInfo.test.tsx`

### Arquivos a Modificar (2)
1. `frontend/src/contexts/checkout/CheckoutContext.tsx`
2. `frontend/src/components/checkout/PaymentFields.tsx`

### Dependências Necessárias
Nenhuma dependência externa adicional necessária.

### Estimativa de Esforço
- **Prioridade Alta**: 2-3 dias (16-24 horas)
- **Prioridade Média**: 1-2 dias (8-16 horas)
- **Prioridade Baixa**: 1 dia (8 horas)
- **Total**: 4-6 dias (32-48 horas)

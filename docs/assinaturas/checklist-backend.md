# Checklist Backend - Regras de Assinatura

## VALIDAÇÕES DE DADOS

### ✅ IMPLEMENTADO CORRETAMENTE

#### 1. Validação de Tipo de Cliente
- **Arquivo**: `backend/src/modules/payment/dto/create-payment.dto.ts:51-53`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
@ValidateIf((o: CreatePaymentDto) => o.isCompany === true)
@IsString()
companyCnpj?: string;
```
- **Teste**: ✅ CNPJ obrigatório apenas se `isCompany = true`

#### 2. Validação de Método de Pagamento
- **Arquivo**: `backend/src/modules/payment/dto/create-payment.dto.ts:177-180`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
@IsEnum(PaymentMethod, {
  message: 'O pagamento deve ser credit_card ou boleto',
})
paymentMethod: PaymentMethod;
```
- **Teste**: ✅ Valida métodos de pagamento aceitos

#### 3. Força Parcela Única para Mensais
- **Arquivo**: `backend/src/modules/payment/payment.service.ts:179-182`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
// Planos mensais sempre serão 1 parcela independente do método
if (!isYearly) {
  installments = 1;
}
```
- **Teste**: ✅ Força `installments = 1` para planos mensais

#### 4. Data de Cobrança para Cartão
- **Arquivo**: `backend/src/modules/payment/payment.service.ts:54-56`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
if (paymentMethod === PaymentMethod.CREDIT_CARD) {
  billingDay = now.getDate();
}
```
- **Teste**: ✅ Cartão sempre usa data atual

## CÁLCULOS FINANCEIROS

### ✅ IMPLEMENTADO CORRETAMENTE

#### 1. Cálculo de Valores Proporcionais
- **Arquivo**: `backend/src/modules/payment/payment.service.ts:192-200`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
const [firstInstallmentAmount, remainingInstallmentAmount] =
  calculateYearlyInstallmentsAmount({
    items: newSubscription.items,
    installments,
    billingDay,
    creditDiscount: 0,
    endDate: fromFirebaseTimestamp(newSubscription.currentPeriodEnd),
    paymentMethod,
  });
```
- **Teste**: ✅ Calcula primeira parcela proporcional

#### 2. Cálculo de Crédito de Upgrade
- **Arquivo**: `backend/src/modules/payment/utils/upgrade-calculator.ts:134-152`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
calculateSubscriptionCredit(subscription: QISubscription, amountPaid: number) {
  const totalValue = subscription.items.reduce(
    (acc, item) => acc + item.totalPrice, 0
  );
  
  return this.calculateCycleCredit(
    subscription.startDate.toDate(),
    subscription.currentPeriodEnd.toDate(),
    new Date(),
    totalValue,
    amountPaid,
  );
}
```
- **Teste**: ✅ Calcula crédito baseado em faturas pagas

#### 3. Cálculo de Datas de Vencimento
- **Arquivo**: `backend/src/modules/payment/utils/payment.utils.ts:127-149`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
if (installmentNumber === 1) {
  // Primeira parcela: 3 dias após a data atual
  const dueDays = isRenewal ? 0 : parseInt(process.env.QI_DAY_DUE || '3');
  dueDate = new Date(addDays(dueDate.getTime(), dueDays));
} else {
  // Da terceira parcela em diante
  const secondInstallmentDate = calculateNextBillingDate(billingDay, dueDate.getTime());
  // ... lógica para parcelas subsequentes
}
```
- **Teste**: ✅ Primeira parcela em 3 dias, demais no dia escolhido

## GESTÃO DE ASSINATURAS

### ✅ IMPLEMENTADO CORRETAMENTE

#### 1. Agendamento de Upgrades
- **Arquivo**: `backend/src/modules/subscription/subscription.service.ts:89-105`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
async scheduleUpgradeSubscription(
  subscriptionId: string,
  newSubscription: Omit<QISubscription, 'id'>,
): Promise<boolean> {
  await this.subscriptionRepository.update(subscriptionId, {
    scheduledAction: QIScheduledAction.CANCEL,
  });

  await this.createSubscription({
    ...newSubscription,
    status: QISubscriptionStatus.FUTURE,
    scheduledAction: QIScheduledAction.ACTIVATE,
  });
  return true;
}
```
- **Teste**: ✅ Agenda cancelamento da atual e criação da nova

#### 2. Detecção de Upgrades
- **Arquivo**: `backend/src/modules/payment/payment.service.ts:61-72`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
// Verifica se é um upgrade (tem assinatura ativa)
const activeSubscription = await this.subscriptionService.getActiveSubscription(accountId);

if (activeSubscription) {
  Logger.log('ASSINATURA ATIVA ✅');
  return await this.upgradeService.processUpgradeFromSubscription(
    activeSubscription,
    account,
    createPaymentDto,
  );
}
```
- **Teste**: ✅ Detecta assinatura ativa e processa como upgrade

#### 3. Criação de Pedidos por Método
- **Arquivo**: `backend/src/modules/payment/payment.service.ts:286-304`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
case PaymentMethod.BOLETO:
  // Para Boleto, criamos um pedido por parcela
  const docNumber = randomUUID().replace(/-/g, '').slice(0, 16);
  orderData.payments.push({
    payment_method: 'boleto',
    boleto: {
      instructions: 'Pagar até o vencimento',
      document_number: docNumber,
      type: 'DM',
      due_at: toFirebaseTimestamp(dueDate)!.toDate().toISOString(),
    },
    split: split?.data ? split.data.rules : undefined,
  });
```
- **Teste**: ✅ Cria pedido separado para cada parcela de boleto/PIX

### ❌ PROBLEMAS IDENTIFICADOS

#### 1. Cobrança 3 Dias Antes Não Implementada
- **Problema**: Não existe sistema automático para cobrança 3 dias antes do vencimento
- **Impacto**: Renovações podem falhar por falta de saldo/cartão vencido
- **Arquivos Afetados**: 
  - Todos os serviços de pagamento
  - Sistema de renovação automática
- **Status**: ❌ NÃO IMPLEMENTADO

#### 2. Sistema de Scheduler Não Existe
- **Problema**: Assinaturas agendadas podem não ser ativadas automaticamente
- **Impacto**: Usuários ficam sem serviço após upgrade agendado
- **Exemplo**: Upgrade de mensal para anual não ativa automaticamente
- **Status**: ❌ NÃO IMPLEMENTADO

#### 3. Regras de Aplicação Inconsistentes
- **Arquivo**: `backend/src/modules/upgrade/upgrade.service.ts:314-320`
- **Problema**: Regras conflitantes para ANUAL→MENSAL e DOWNGRADE
- **Impacto**: Comportamento imprevisível
- **Código Problemático**:
```typescript
// TODO: verificar esse ponto pois aparentemente está errado em atualizar o status como pago 
// pois a cobrança deveria ser gerada somente na data de fim da assinatura atual
if (remainingAmount <= 0) {
  if (isChangingBillingCycle) {
    this.log('Mudança de ciclo de faturamento detectada ✅');
  } else {
    this.log('Downgrade detectado ✅');
  }
}
```
- **Status**: ❌ INCONSISTENTE

#### 4. Webhook Não Aplica Upgrade Imediato
- **Problema**: Não verifica se deve aplicar upgrade ao receber pagamento
- **Impacto**: Upgrades podem não ser aplicados imediatamente
- **Arquivo**: Sistema de webhooks
- **Status**: ❌ NÃO VERIFICADO

## MODIFICAÇÕES NECESSÁRIAS

### 1. Sistema de Cobrança Automática
```typescript
// backend/src/modules/billing/billing-scheduler.service.ts
@Injectable()
export class BillingSchedulerService {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly paymentService: PaymentService,
    private readonly notificationService: NotificationService,
  ) {}

  @Cron('0 6 * * *') // Todo dia às 6h da manhã
  async processPreBillingCharges() {
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
    
    // Buscar assinaturas que vencem em 3 dias
    const subscriptionsToCharge = await this.subscriptionService
      .getSubscriptionsWithBillingDate(threeDaysFromNow);
    
    for (const subscription of subscriptionsToCharge) {
      try {
        await this.processPreBillingCharge(subscription);
      } catch (error) {
        Logger.error(`Erro ao processar cobrança antecipada: ${subscription.id}`, error);
      }
    }
  }
  
  private async processPreBillingCharge(subscription: QISubscription) {
    // Verificar se já foi processada
    if (subscription.metadata?.preBillingProcessed) {
      return;
    }
    
    // Gerar cobrança baseada no método de pagamento
    switch (subscription.paymentMethod) {
      case QIPaymentMethod.BOLETO:
      case QIPaymentMethod.PIX:
        await this.generatePreBillingCharge(subscription);
        break;
      case QIPaymentMethod.CREDIT_CARD:
        // Cartão é processado automaticamente no vencimento
        await this.scheduleCardCharge(subscription);
        break;
    }
    
    // Marcar como processada
    await this.subscriptionService.updateSubscription(subscription.id, {
      metadata: {
        ...subscription.metadata,
        preBillingProcessed: true,
        preBillingDate: new Date().toISOString(),
      }
    });
    
    // Enviar notificação ao usuário
    await this.notificationService.sendBillingNotification(subscription);
  }
  
  private async generatePreBillingCharge(subscription: QISubscription) {
    // Criar pedido de renovação
    const renewalOrder = await this.paymentService.createRenewalOrder(subscription);
    
    // Agendar retry se não pago em 3 dias
    await this.scheduleRetryIfNotPaid(subscription.id, renewalOrder.id);
  }
}
```

### 2. Sistema de Scheduler de Ativação
```typescript
// backend/src/modules/subscription/subscription-scheduler.service.ts
@Injectable()
export class SubscriptionSchedulerService {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly accountService: AccountService,
  ) {}

  @Cron('0 0 * * *') // Todo dia à meia-noite
  async processScheduledActions() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    try {
      // Processar ativações agendadas
      await this.processScheduledActivations(today);
      
      // Processar cancelamentos agendados
      await this.processScheduledCancellations(today);
      
      // Processar expirações
      await this.processScheduledExpirations(today);
      
    } catch (error) {
      Logger.error('Erro ao processar ações agendadas', error);
    }
  }
  
  private async processScheduledActivations(date: Date) {
    const subscriptionsToActivate = await this.subscriptionService
      .getSubscriptionsToActivate(date);
    
    Logger.log(`Processando ${subscriptionsToActivate.length} ativações agendadas`);
    
    for (const subscription of subscriptionsToActivate) {
      try {
        await this.activateSubscription(subscription);
        Logger.log(`Assinatura ativada: ${subscription.id}`);
      } catch (error) {
        Logger.error(`Erro ao ativar assinatura ${subscription.id}`, error);
      }
    }
  }
  
  private async activateSubscription(subscription: QISubscription) {
    // Ativar nova assinatura
    await this.subscriptionService.updateSubscription(subscription.id, {
      status: QISubscriptionStatus.ACTIVE,
      scheduledAction: null,
      activatedAt: admin.firestore.Timestamp.now(),
    });
    
    // Atualizar conta com novo plano
    await this.accountService.updateAccountPlan(
      subscription.accountId,
      subscription.planId
    );
    
    // Cancelar assinatura anterior se existir
    if (subscription.metadata?.previousSubscriptionId) {
      await this.subscriptionService.updateSubscription(
        subscription.metadata.previousSubscriptionId,
        {
          status: QISubscriptionStatus.CANCELED,
          canceledAt: admin.firestore.Timestamp.now(),
        }
      );
    }
  }
}
```

### 3. Regras Consistentes de Upgrade
```typescript
// backend/src/modules/upgrade/upgrade-rules.service.ts
@Injectable()
export class UpgradeRulesService {
  
  determineUpgradeApplication(
    currentSubscription: QISubscription,
    newPlan: any,
    upgradeValue: number
  ): UpgradeApplicationRule {
    
    const isChangingBillingCycle = 
      currentSubscription.billingInterval === QIBillingInterval.YEARLY !== newPlan.isYearly;
    
    const isUpgrade = upgradeValue > 0;
    const isDowngrade = upgradeValue < 0;
    
    // REGRA 1: Mudança de ciclo de faturamento sempre é agendada
    if (isChangingBillingCycle) {
      return {
        type: 'SCHEDULED',
        reason: 'BILLING_CYCLE_CHANGE',
        activationDate: currentSubscription.currentPeriodEnd.toDate(),
        immediateCharge: false,
        message: 'Upgrade será aplicado no fim do período atual'
      };
    }
    
    // REGRA 2: Upgrade no mesmo ciclo é imediato
    if (isUpgrade) {
      return {
        type: 'IMMEDIATE',
        reason: 'SAME_CYCLE_UPGRADE',
        activationDate: new Date(),
        immediateCharge: true,
        message: 'Upgrade aplicado imediatamente após o pagamento'
      };
    }
    
    // REGRA 3: Downgrade sempre é agendado
    if (isDowngrade) {
      return {
        type: 'SCHEDULED',
        reason: 'DOWNGRADE',
        activationDate: currentSubscription.currentPeriodEnd.toDate(),
        immediateCharge: false,
        message: 'Downgrade será aplicado no fim do período atual'
      };
    }
    
    // REGRA 4: Mesmo valor (customização) é imediato
    return {
      type: 'IMMEDIATE',
      reason: 'CUSTOMIZATION',
      activationDate: new Date(),
      immediateCharge: false,
      message: 'Alterações aplicadas imediatamente'
    };
  }
  
  validateUpgradeRules(
    currentSubscription: QISubscription,
    createPaymentDto: CreatePaymentDto
  ): ValidationResult {
    const errors: string[] = [];
    
    // Validar se pode fazer upgrade
    if (currentSubscription.status !== QISubscriptionStatus.ACTIVE) {
      errors.push('Apenas assinaturas ativas podem ser atualizadas');
    }
    
    // Validar mudança de método de pagamento
    if (currentSubscription.paymentMethod !== createPaymentDto.paymentMethod) {
      // Permitir mudança apenas em casos específicos
      if (!this.isPaymentMethodChangeAllowed(currentSubscription, createPaymentDto)) {
        errors.push('Mudança de método de pagamento não permitida para este tipo de upgrade');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
```

### 4. Melhorar Webhook Handler
```typescript
// backend/src/modules/webhook/webhook.service.ts
async handlePaymentWebhook(webhookData: any) {
  const { subscriptionId, status, installmentNumber, orderId } = webhookData;
  
  if (status === 'paid') {
    const subscription = await this.subscriptionService.getSubscription(subscriptionId);
    
    if (!subscription) {
      Logger.error(`Assinatura não encontrada: ${subscriptionId}`);
      return;
    }
    
    // Verificar se é upgrade imediato
    const upgradeRule = subscription.metadata?.upgradeRule;
    
    if (upgradeRule?.type === 'IMMEDIATE' && installmentNumber === 1) {
      await this.applyUpgradeImmediately(subscription);
    }
    
    // Atualizar status da assinatura
    await this.updateSubscriptionPaymentStatus(subscription, webhookData);
  }
}

private async applyUpgradeImmediately(subscription: QISubscription) {
  try {
    // Ativar novo plano na conta
    await this.accountService.updateAccountPlan(
      subscription.accountId,
      subscription.planId
    );
    
    // Cancelar assinatura anterior se existir
    if (subscription.metadata?.previousSubscriptionId) {
      await this.subscriptionService.updateSubscription(
        subscription.metadata.previousSubscriptionId,
        {
          status: QISubscriptionStatus.CANCELED,
          canceledAt: admin.firestore.Timestamp.now(),
        }
      );
    }
    
    // Ativar nova assinatura
    await this.subscriptionService.updateSubscription(subscription.id, {
      status: QISubscriptionStatus.ACTIVE,
      activatedAt: admin.firestore.Timestamp.now(),
    });
    
    Logger.log(`Upgrade aplicado imediatamente: ${subscription.id}`);
    
  } catch (error) {
    Logger.error(`Erro ao aplicar upgrade imediato: ${subscription.id}`, error);
    throw error;
  }
}
```

## ARQUIVOS A SEREM CRIADOS

### Novos Serviços
1. `backend/src/modules/billing/billing-scheduler.service.ts` - Cobrança automática
2. `backend/src/modules/billing/billing-scheduler.module.ts` - Módulo de cobrança
3. `backend/src/modules/subscription/subscription-scheduler.service.ts` - Ativação automática
4. `backend/src/modules/upgrade/upgrade-rules.service.ts` - Regras consistentes
5. `backend/src/common/jobs/subscription-jobs.service.ts` - Jobs de assinatura

### DTOs e Types
1. `backend/src/modules/billing/dto/billing-notification.dto.ts` - Notificações
2. `backend/src/modules/upgrade/types/upgrade-rules.types.ts` - Tipos de upgrade

## ARQUIVOS A SEREM MODIFICADOS

### Modificações Obrigatórias
1. `backend/src/modules/payment/payment.service.ts` - Integrar scheduler de cobrança
2. `backend/src/modules/upgrade/upgrade.service.ts` - Usar regras consistentes
3. `backend/src/modules/webhook/webhook.service.ts` - Melhorar handler
4. `backend/src/modules/subscription/subscription.service.ts` - Adicionar métodos de agendamento
5. `backend/src/app.module.ts` - Registrar novos módulos

### Configurações
1. `backend/src/config/` - Adicionar configurações de jobs
2. `backend/.env` - Variáveis de ambiente para scheduler

## DEPENDÊNCIAS NECESSÁRIAS

```json
{
  "bull": "^4.10.4",
  "@nestjs/bull": "^10.0.1", 
  "node-cron": "^3.0.2",
  "@nestjs/schedule": "^4.0.0",
  "redis": "^4.6.0"
}
```

## TESTES NECESSÁRIOS

### Testes de Unidade
```typescript
// backend/src/modules/subscription/subscription-scheduler.service.spec.ts
describe('SubscriptionSchedulerService', () => {
  it('should activate scheduled subscriptions', async () => {
    const mockSubscription = createMockSubscription({
      status: QISubscriptionStatus.FUTURE,
      scheduledAction: QIScheduledAction.ACTIVATE
    });
    
    await service.processScheduledActivations(new Date());
    
    expect(subscriptionService.updateSubscription).toHaveBeenCalledWith(
      mockSubscription.id,
      expect.objectContaining({
        status: QISubscriptionStatus.ACTIVE,
        scheduledAction: null
      })
    );
  });
});
```

## RESUMO BACKEND

### ✅ Funcionalidades Corretas (70%)
- Validação de dados de entrada
- Cálculos financeiros (proporcionais, créditos)
- Agendamento básico de assinaturas
- Detecção de upgrades
- Criação de pedidos por método

### ❌ Funcionalidades com Problemas (30%)
- Sistema de cobrança automática 3 dias antes
- Scheduler de ativação de assinaturas
- Regras consistentes de upgrade
- Webhook para aplicação imediata
- Sistema de jobs/cron

### 🎯 Prioridades
1. **Crítica**: Sistema de scheduler de ativação
2. **Crítica**: Regras consistentes de upgrade  
3. **Alta**: Melhorar webhooks
4. **Média**: Sistema de cobrança automática

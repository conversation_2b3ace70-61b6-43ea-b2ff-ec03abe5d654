# Checklist Frontend - Regras de Assinatura

## VALIDAÇÕES DE INTERFACE

### ✅ IMPLEMENTADO CORRETAMENTE

#### 1. Boleto Apenas para Pessoa Jurídica
- **Arquivo**: `frontend/src/components/checkout/PaymentFields.tsx:239-255`
- **Status**: ✅ CORRETO
- **Implementação**: 
```tsx
{isCompany && (
  <div className="flex-1">
    <RadioGroupItem value={PaymentMethod.BOLETO} id={PaymentMethod.BOLETO} />
    <Label htmlFor={PaymentMethod.BOLETO}>Boleto Bancário</Label>
  </div>
)}
```
- **Teste**: ✅ Interface mostra boleto apenas quando `isCompany = true`

#### 2. Parcelamento Apenas para Planos Anuais
- **Arquivo**: `frontend/src/components/checkout/PaymentFields.tsx:352-370`
- **Status**: ✅ CORRETO
- **Implementação**:
```tsx
<Select disabled={!isYearly}>
  <SelectContent>
    {installments.map((installment) => (
      <SelectItem key={installment} value={installment.toString()}>
        {installment}x {formatCurrency(total / installment)} sem juros
      </SelectItem>
    ))}
  </SelectContent>
</Select>
```
- **Teste**: ✅ Campo de parcelas desabilitado quando plano é mensal

#### 3. Seleção de Data de Vencimento
- **Arquivo**: `frontend/src/components/checkout/PaymentFields.tsx:469-489`
- **Status**: ✅ CORRETO
- **Implementação**:
```tsx
{Number(selectedInstallments) > 1 && paymentMethod !== PaymentMethod.CREDIT_CARD && (
  <div>
    <Label htmlFor="billingDay">Qual o melhor dia para vencimento?</Label>
    <Select>
      {paymentDays.map((day) => (
        <SelectItem key={day} value={day.toString()}>Todo dia {day}</SelectItem>
      ))}
    </Select>
  </div>
)}
```
- **Teste**: ✅ Mostra seleção de data apenas quando parcelado e não é cartão

#### 4. Configuração de Parcelas e Dias
- **Arquivo**: `frontend/src/config/payment.ts:1-2`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
export const installments = [1, 12] // Quantidade de parcelas aceitas
export const paymentDays = [5, 10, 15, 20, 25] // Dias disponíveis para pagamento
```
- **Teste**: ✅ Limita parcelas a 1 ou 12, dias de 5 a 25

### ❌ PROBLEMAS IDENTIFICADOS

#### 1. Falta Validação Rigorosa de Regras
- **Problema**: Não valida regras de negócio antes do envio
- **Impacto**: Usuário pode tentar ações inválidas
- **Arquivos Afetados**: 
  - `frontend/src/contexts/checkout/CheckoutContext.tsx`
  - `frontend/src/components/checkout/PaymentFields.tsx`
- **Status**: ❌ NÃO IMPLEMENTADO

#### 2. Interface Não Explica Regras ao Usuário
- **Problema**: Usuário não entende quando upgrade será aplicado
- **Impacto**: Confusão sobre cobrança e ativação
- **Exemplo**: Upgrade de mensal para anual não explica que será agendado
- **Status**: ❌ NÃO IMPLEMENTADO

#### 3. Cálculo de Valores Não Transparente
- **Arquivo**: `frontend/src/lib/plan.utils.ts:201-219`
- **Problema**: Lógica complexa sem explicação visual
- **Impacto**: Usuário não entende valores mostrados
- **Exemplo**: Valor proporcional não é explicado
- **Status**: ❌ PARCIALMENTE IMPLEMENTADO

#### 4. Validação de Cartão para Data de Vencimento
- **Problema**: Cartão sempre usa data atual, mas interface permite seleção
- **Arquivo**: `frontend/src/components/checkout/PaymentFields.tsx:469`
- **Impacto**: Confusão sobre quando a regra se aplica
- **Status**: ❌ INCONSISTENTE

## FUNCIONALIDADES DE UPGRADE

### ✅ IMPLEMENTADO CORRETAMENTE

#### 1. Detecção de Tipo de Upgrade
- **Arquivo**: `frontend/src/components/PlanCard.tsx:84-92`
- **Status**: ✅ CORRETO
- **Implementação**:
```tsx
const isMonthlyToMonthly = !isCurrentYearly && !plan.isYearly;
const isYearlyToYearly = isCurrentYearly && plan.isYearly;
const isSamePlanType = Math.abs(viewedPlanLevel - currentPlanLevel) < 100;
const isImmediateUpgrade = hasDiff && (isUpgrade && isSamePlanType && (isMonthlyToMonthly || isYearlyToYearly)) || isPartialUpgrade;
```
- **Teste**: ✅ Identifica corretamente tipos de upgrade

#### 2. Cálculo de Valores Proporcionais
- **Arquivo**: `frontend/src/lib/plan.utils.ts:201-219`
- **Status**: ✅ CORRETO
- **Implementação**:
```typescript
const shouldApplyProportionalCalculation = (isUpgrade || isPartialUpgrade) && !!subscription?.endDate;
const upgradeCredit = (isUpgrade || isPartialUpgrade) && !changingBillingCycle && currentPlan?.credit ? currentPlan.credit : 0;
```
- **Teste**: ✅ Calcula valores proporcionais para upgrades

### ❌ PROBLEMAS IDENTIFICADOS

#### 1. Não Mostra Quando Upgrade Será Aplicado
- **Problema**: Interface não informa se upgrade é imediato ou agendado
- **Impacto**: Usuário não sabe quando terá acesso às novas funcionalidades
- **Status**: ❌ NÃO IMPLEMENTADO

#### 2. Não Explica Diferença de Valores
- **Problema**: Não mostra breakdown do cálculo de upgrade
- **Impacto**: Usuário não entende por que paga determinado valor
- **Status**: ❌ NÃO IMPLEMENTADO

## MODIFICAÇÕES NECESSÁRIAS

### 1. Criar Hook de Validação
```typescript
// frontend/src/hooks/useSubscriptionValidation.ts
export function useSubscriptionValidation() {
  const validatePaymentMethod = (
    paymentMethod: PaymentMethod,
    isCompany: boolean,
    isYearly: boolean,
    installments: number
  ): ValidationResult => {
    const errors: string[] = [];
    
    // Boleto apenas para PJ
    if (paymentMethod === PaymentMethod.BOLETO && !isCompany) {
      errors.push('Boleto disponível apenas para pessoa jurídica');
    }
    
    // Parcelamento apenas para anuais
    if (installments > 1 && !isYearly) {
      errors.push('Parcelamento disponível apenas para planos anuais');
    }
    
    // Máximo 12 parcelas
    if (installments > 12) {
      errors.push('Máximo de 12 parcelas permitidas');
    }
    
    // Data de vencimento apenas para parcelado (exceto cartão)
    if (installments > 1 && paymentMethod === PaymentMethod.CREDIT_CARD) {
      // Cartão sempre usa data atual
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };
  
  return { validatePaymentMethod };
}
```

### 2. Componente de Explicação das Regras
```typescript
// frontend/src/components/checkout/SubscriptionRulesInfo.tsx
export function SubscriptionRulesInfo({ 
  paymentMethod, 
  isYearly, 
  installments,
  isUpgrade,
  upgradeType 
}: SubscriptionRulesInfoProps) {
  
  const getRulesForNewSubscription = () => {
    const rules: string[] = [];
    
    if (isYearly) {
      rules.push('Assinatura anual com duração de 365 dias');
      
      if (installments > 1) {
        if (paymentMethod === PaymentMethod.CREDIT_CARD) {
          rules.push('Primeira parcela debitada hoje, demais no mesmo dia dos próximos meses');
        } else {
          rules.push('Primeira parcela com valor proporcional, demais no dia escolhido');
          rules.push('Você tem até 3 dias para pagar cada parcela');
        }
      } else {
        rules.push('Pagamento único do valor total');
        if (paymentMethod !== PaymentMethod.CREDIT_CARD) {
          rules.push('Cobrança será feita 3 dias antes do vencimento');
        }
      }
    } else {
      rules.push('Assinatura mensal com renovação automática a cada 30 dias');
      rules.push('Não é possível parcelar planos mensais');
      if (paymentMethod !== PaymentMethod.CREDIT_CARD) {
        rules.push('Cobrança será feita 3 dias antes do vencimento');
      }
    }
    
    return rules;
  };
  
  const getRulesForUpgrade = () => {
    const rules: string[] = [];
    
    switch (upgradeType) {
      case 'MONTHLY_TO_MONTHLY':
        rules.push('Upgrade aplicado imediatamente após o pagamento');
        rules.push('Você recebe crédito proporcional do plano atual');
        rules.push('Paga apenas a diferença até o fim do período atual');
        break;
        
      case 'YEARLY_TO_YEARLY':
        rules.push('Upgrade aplicado imediatamente após o pagamento');
        rules.push('Você recebe crédito proporcional do plano atual');
        rules.push('Nova assinatura termina na mesma data da atual');
        break;
        
      case 'MONTHLY_TO_YEARLY':
        rules.push('Upgrade será aplicado no fim do período atual');
        rules.push('Plano anual inicia quando o mensal terminar');
        rules.push('Sem cobrança imediata - pagamento na data de ativação');
        break;
        
      case 'YEARLY_TO_MONTHLY':
        rules.push('Upgrade será aplicado no fim do período atual');
        rules.push('Plano mensal inicia quando o anual terminar');
        rules.push('Sem cobrança imediata - pagamento na data de ativação');
        break;
    }
    
    return rules;
  };
  
  const rules = isUpgrade ? getRulesForUpgrade() : getRulesForNewSubscription();
  
  return (
    <Alert className="mb-4 border-blue-200 bg-blue-50">
      <Info className="h-4 w-4 text-blue-600" />
      <AlertTitle className="text-blue-900">Como funciona sua assinatura</AlertTitle>
      <AlertDescription>
        <ul className="list-disc list-inside space-y-1 mt-2 text-blue-800">
          {rules.map((rule, index) => (
            <li key={index} className="text-sm">{rule}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}
```

### 3. Componente de Breakdown de Valores
```typescript
// frontend/src/components/checkout/PriceBreakdown.tsx
export function PriceBreakdown({ 
  originalPrice, 
  credit, 
  finalPrice, 
  isUpgrade 
}: PriceBreakdownProps) {
  
  if (!isUpgrade) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium mb-2">Resumo do pagamento</h4>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span>Valor do plano:</span>
            <span>{formatCurrency(originalPrice)}</span>
          </div>
          <div className="flex justify-between font-medium border-t pt-1">
            <span>Total a pagar:</span>
            <span>{formatCurrency(finalPrice)}</span>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <h4 className="font-medium mb-2">Resumo do upgrade</h4>
      <div className="space-y-1 text-sm">
        <div className="flex justify-between">
          <span>Valor do novo plano:</span>
          <span>{formatCurrency(originalPrice)}</span>
        </div>
        {credit > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Crédito do plano atual:</span>
            <span>-{formatCurrency(credit)}</span>
          </div>
        )}
        <div className="flex justify-between font-medium border-t pt-1">
          <span>Total a pagar agora:</span>
          <span>{formatCurrency(finalPrice)}</span>
        </div>
      </div>
    </div>
  );
}
```

## ARQUIVOS A SEREM MODIFICADOS

### Modificações Obrigatórias
1. `frontend/src/components/checkout/PaymentFields.tsx` - Adicionar validações e componente de regras
2. `frontend/src/contexts/checkout/CheckoutContext.tsx` - Integrar hook de validação
3. `frontend/src/components/checkout/CurrentSummary.tsx` - Adicionar breakdown de valores

### Arquivos a Serem Criados
1. `frontend/src/hooks/useSubscriptionValidation.ts` - Hook de validação
2. `frontend/src/components/checkout/SubscriptionRulesInfo.tsx` - Componente de regras
3. `frontend/src/components/checkout/PriceBreakdown.tsx` - Breakdown de valores
4. `frontend/src/utils/subscription-rules.utils.ts` - Utilitários de regras

## TESTES NECESSÁRIOS

### Testes de Componente
```typescript
// frontend/src/components/checkout/__tests__/PaymentFields.test.tsx
describe('PaymentFields', () => {
  it('should show boleto option only for companies', () => {
    render(<PaymentFields isCompany={false} />);
    expect(screen.queryByText('Boleto Bancário')).not.toBeInTheDocument();
    
    render(<PaymentFields isCompany={true} />);
    expect(screen.getByText('Boleto Bancário')).toBeInTheDocument();
  });
  
  it('should disable installments for monthly plans', () => {
    render(<PaymentFields isYearly={false} />);
    expect(screen.getByRole('combobox', { name: /parcelas/i })).toBeDisabled();
  });
  
  it('should show billing day selection for installments > 1 and non-credit card', () => {
    render(<PaymentFields isYearly={true} installments={12} paymentMethod="pix" />);
    expect(screen.getByText('Qual o melhor dia para vencimento?')).toBeInTheDocument();
  });
});
```

## RESUMO FRONTEND

### ✅ Funcionalidades Corretas (60%)
- Validação de boleto para PJ
- Parcelamento apenas para anuais
- Seleção de data de vencimento
- Detecção de tipos de upgrade
- Cálculo de valores proporcionais

### ❌ Funcionalidades com Problemas (40%)
- Validação rigorosa de regras
- Explicação das regras ao usuário
- Transparência nos cálculos
- Feedback sobre aplicação de upgrades
- Consistência na interface de datas

### 🎯 Prioridades
1. **Alta**: Componente de explicação das regras
2. **Alta**: Hook de validação rigorosa
3. **Média**: Breakdown de valores
4. **Baixa**: Testes automatizados

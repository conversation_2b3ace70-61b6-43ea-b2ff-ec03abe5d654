# Modificações Necessárias para Atender às Regras de Assinatura

## 1. COBRANÇA 3 DIAS ANTES DO VENCIMENTO

### Problema

Atualmente não existe lógica para cobrança automática 3 dias antes do vencimento para renovações.

### Solução

**Backend:**

```typescript
// Criar serviço de cobrança automática
// Arquivo: backend/src/modules/billing/billing-scheduler.service.ts

@Injectable()
export class BillingSchedulerService {
  async schedulePreBillingNotification(subscription: QISubscription) {
    const billingDate = subscription.nextBillingDate.toDate();
    const notificationDate = new Date(billingDate);
    notificationDate.setDate(notificationDate.getDate() - 3);

    // Agendar job para 3 dias antes
    await this.scheduleJob(`pre-billing-${subscription.id}`, notificationDate, {
      subscriptionId: subscription.id,
      action: "PRE_BILLING_CHARGE",
    });
  }

  async processPreBillingCharge(subscriptionId: string) {
    // Lógica para processar cobrança 3 dias antes
    // Gerar invoice, enviar notificação, etc.
  }
}
```

**Implementar em:**

- `payment.service.ts` - Agendar ao criar assinatura
- `upgrade.service.ts` - Agendar ao fazer upgrade
- Criar job scheduler (Bull/Agenda) para executar tarefas agendadas

## 2. APLICAÇÃO IMEDIATA VS AGENDADA - CORREÇÃO DE INCONSISTÊNCIAS

### Problema

Regras conflitantes para ANUAL PARA MENSAL e DOWNGRADE.

### Solução

**Clarificar e implementar regras consistentes:**

```typescript
// Arquivo: backend/src/modules/upgrade/upgrade-rules.service.ts

@Injectable()
export class UpgradeRulesService {
  determineUpgradeApplication(
    currentSubscription: QISubscription,
    newPlan: any,
    upgradeType: "UPGRADE" | "DOWNGRADE"
  ): "IMMEDIATE" | "SCHEDULED" {
    const isChangingBillingCycle =
      (currentSubscription.billingInterval === QIBillingInterval.YEARLY) !==
      newPlan.isYearly;

    // Regras claras:
    if (isChangingBillingCycle) {
      return "SCHEDULED"; // Sempre agendado quando muda ciclo
    }

    if (upgradeType === "UPGRADE") {
      return "IMMEDIATE"; // Upgrades no mesmo ciclo são imediatos
    }

    if (upgradeType === "DOWNGRADE") {
      return "SCHEDULED"; // Downgrades sempre agendados
    }

    return "SCHEDULED";
  }
}
```

## 3. SISTEMA DE SCHEDULER PARA ATIVAÇÃO DE ASSINATURAS

### Problema

Não existe sistema verificado para ativar assinaturas agendadas.

### Solução

**Criar scheduler de ativação:**

```typescript
// Arquivo: backend/src/modules/subscription/subscription-scheduler.service.ts

@Injectable()
export class SubscriptionSchedulerService {
  @Cron("0 0 * * *") // Todo dia à meia-noite
  async processScheduledSubscriptions() {
    const today = new Date();

    // Buscar assinaturas para ativar hoje
    const subscriptionsToActivate =
      await this.subscriptionService.getSubscriptionsToActivate(today);

    for (const subscription of subscriptionsToActivate) {
      await this.activateSubscription(subscription);
    }

    // Buscar assinaturas para cancelar hoje
    const subscriptionsToCancel =
      await this.subscriptionService.getSubscriptionsToCancel(today);

    for (const subscription of subscriptionsToCancel) {
      await this.cancelSubscription(subscription);
    }
  }

  private async activateSubscription(subscription: QISubscription) {
    // Ativar nova assinatura
    await this.subscriptionService.updateSubscription(subscription.id, {
      status: QISubscriptionStatus.ACTIVE,
      scheduledAction: null,
    });

    // Atualizar conta com novo plano
    await this.accountService.updateAccountPlan(
      subscription.accountId,
      subscription.planId
    );
  }
}
```

## 4. WEBHOOKS DE UPGRADE - APLICAÇÃO IMEDIATA

### Problema

Não verificado se webhooks aplicam mudanças imediatamente ao receber pagamento.

### Solução

**Melhorar webhook handler:**

```typescript
// Arquivo: backend/src/modules/webhook/webhook.service.ts

async handlePaymentWebhook(webhookData: any) {
  const { subscriptionId, status, installmentNumber } = webhookData;

  if (status === 'paid') {
    const subscription = await this.subscriptionService.getSubscription(subscriptionId);

    // Se é upgrade imediato e primeira parcela
    if (subscription.metadata?.upgradeType === 'IMMEDIATE' && installmentNumber === 1) {
      await this.applyUpgradeImmediately(subscription);
    }
  }
}

private async applyUpgradeImmediately(subscription: QISubscription) {
  // Ativar novo plano imediatamente
  await this.accountService.updateAccountPlan(
    subscription.accountId,
    subscription.planId
  );

  // Cancelar assinatura anterior se existir
  if (subscription.metadata?.previousSubscriptionId) {
    await this.subscriptionService.cancelSubscription(
      subscription.metadata.previousSubscriptionId
    );
  }
}
```

## 5. VALIDAÇÕES MAIS RIGOROSAS NO FRONTEND

### Problema

Falta validação mais rigorosa das regras de negócio no frontend.

### Solução

**Criar hook de validação:**

```typescript
// Arquivo: frontend/src/hooks/useSubscriptionValidation.ts

export function useSubscriptionValidation() {
  const validatePaymentMethod = (
    paymentMethod: PaymentMethod,
    isCompany: boolean,
    isYearly: boolean,
    installments: number
  ): ValidationResult => {
    // Boleto apenas para PJ
    if (paymentMethod === PaymentMethod.BOLETO && !isCompany) {
      return {
        valid: false,
        message: "Boleto disponível apenas para pessoa jurídica",
      };
    }

    // Parcelamento apenas para anuais
    if (installments > 1 && !isYearly) {
      return {
        valid: false,
        message: "Parcelamento disponível apenas para planos anuais",
      };
    }

    // Máximo 12 parcelas
    if (installments > 12) {
      return { valid: false, message: "Máximo de 12 parcelas permitidas" };
    }

    return { valid: true };
  };

  const validateUpgrade = (
    currentPlan: Plan,
    newPlan: Plan
  ): UpgradeValidationResult => {
    const isChangingBillingCycle = currentPlan.isYearly !== newPlan.isYearly;
    const isUpgrade = newPlan.totalPrice > currentPlan.totalPrice;

    return {
      isChangingBillingCycle,
      isUpgrade,
      applicationType: determineApplicationType(currentPlan, newPlan),
      message: getUpgradeMessage(currentPlan, newPlan),
    };
  };

  return { validatePaymentMethod, validateUpgrade };
}
```

## 6. COMPONENTE DE EXPLICAÇÃO DAS REGRAS

### Problema

Interface não explica claramente as regras ao usuário.

### Solução

**Criar componente informativo:**

```typescript
// Arquivo: frontend/src/components/checkout/SubscriptionRulesInfo.tsx

export function SubscriptionRulesInfo({
  paymentMethod,
  isYearly,
  isUpgrade,
  upgradeType,
}: SubscriptionRulesInfoProps) {
  const getRulesText = () => {
    if (isUpgrade) {
      return getUpgradeRulesText(upgradeType);
    }

    return getNewSubscriptionRulesText(paymentMethod, isYearly);
  };

  return (
    <Card className="p-4 bg-blue-50 border-blue-200">
      <div className="flex items-start gap-3">
        <Info className="h-5 w-5 text-blue-600 mt-0.5" />
        <div>
          <h4 className="font-medium text-blue-900 mb-2">
            Como funciona sua assinatura
          </h4>
          <div className="text-sm text-blue-800 space-y-1">
            {getRulesText().map((rule, index) => (
              <div key={index} className="flex items-start gap-2">
                <span className="text-blue-600">•</span>
                <span>{rule}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
}
```

## 7. TESTES AUTOMATIZADOS

### Problema

Não foram encontrados testes específicos para as regras de negócio.

### Solução

**Criar suíte de testes:**

```typescript
// Arquivo: backend/src/modules/subscription/subscription.service.spec.ts

describe("Subscription Rules", () => {
  describe("First Subscription", () => {
    it("should not allow installments for monthly plans", async () => {
      const dto = createPaymentDto({ isYearly: false, installments: 12 });
      const result = await paymentService.subscribe(dto, account);
      expect(result.subscription.installments).toBe(1);
    });

    it("should allow installments for yearly plans", async () => {
      const dto = createPaymentDto({ isYearly: true, installments: 12 });
      const result = await paymentService.subscribe(dto, account);
      expect(result.subscription.installments).toBe(12);
    });

    it("should only allow boleto for companies", async () => {
      const dto = createPaymentDto({
        paymentMethod: PaymentMethod.BOLETO,
        isCompany: false,
      });
      await expect(paymentService.subscribe(dto, account)).rejects.toThrow(
        "Boleto disponível apenas para pessoa jurídica"
      );
    });
  });

  describe("Upgrades", () => {
    it("should apply immediate upgrade for same billing cycle", async () => {
      // Teste para upgrade imediato
    });

    it("should schedule upgrade for different billing cycles", async () => {
      // Teste para upgrade agendado
    });
  });
});
```

## 8. DOCUMENTAÇÃO UNIFICADA

### Problema

Regras conflitantes entre documentos.

### Solução

**Criar documento único e autoritativo:**

- Consolidar todas as regras em um documento master
- Remover documentos conflitantes
- Criar diagramas de fluxo para cada cenário
- Implementar validação automática da documentação vs código

## PRIORIDADE DE IMPLEMENTAÇÃO

### 🔴 ALTA PRIORIDADE

1. Correção de inconsistências nas regras (Item 2)
2. Sistema de scheduler (Item 3)
3. Webhooks de upgrade (Item 4)

### 🟡 MÉDIA PRIORIDADE

4. Cobrança 3 dias antes (Item 1)
5. Validações no frontend (Item 5)
6. Componente informativo (Item 6)

### 🟢 BAIXA PRIORIDADE

7. Testes automatizados (Item 7)
8. Documentação unificada (Item 8)

## ARQUIVOS QUE PRECISAM SER CRIADOS

### Backend

```
backend/src/modules/billing/
├── billing-scheduler.service.ts
├── billing-scheduler.module.ts
└── dto/billing-notification.dto.ts

backend/src/modules/subscription/
├── subscription-scheduler.service.ts
└── subscription-rules.service.ts

backend/src/modules/upgrade/
└── upgrade-rules.service.ts

backend/src/common/jobs/
├── subscription-jobs.service.ts
└── billing-jobs.service.ts
```

### Frontend

```
frontend/src/hooks/
└── useSubscriptionValidation.ts

frontend/src/components/checkout/
├── SubscriptionRulesInfo.tsx
└── UpgradeWarning.tsx

frontend/src/utils/
└── subscription-rules.utils.ts
```

### Testes

```
backend/src/modules/subscription/
├── subscription.service.spec.ts
├── subscription-scheduler.service.spec.ts
└── upgrade-rules.service.spec.ts

frontend/src/components/checkout/
└── __tests__/subscription-rules.test.tsx
```

## ARQUIVOS QUE PRECISAM SER MODIFICADOS

### Backend

- `backend/src/modules/payment/payment.service.ts` - Adicionar agendamento de cobrança
- `backend/src/modules/upgrade/upgrade.service.ts` - Implementar regras consistentes
- `backend/src/modules/webhook/webhook.service.ts` - Melhorar handler de pagamento
- `backend/src/modules/subscription/subscription.service.ts` - Adicionar métodos de agendamento
- `backend/src/app.module.ts` - Registrar novos módulos e jobs

### Frontend

- `frontend/src/components/checkout/PaymentFields.tsx` - Adicionar validações
- `frontend/src/contexts/checkout/CheckoutContext.tsx` - Integrar validações
- `frontend/src/components/checkout/CurrentSummary.tsx` - Mostrar regras aplicáveis
- `frontend/src/lib/plan.utils.ts` - Adicionar utilitários de validação

## DEPENDÊNCIAS EXTERNAS NECESSÁRIAS

### Backend

```json
{
  "bull": "^4.10.4",
  "@nestjs/bull": "^10.0.1",
  "node-cron": "^3.0.2",
  "@nestjs/schedule": "^4.0.0"
}
```

### Frontend

```json
{
  "@hookform/resolvers": "^3.3.2",
  "zod": "^3.22.4"
}
```

## CONFIGURAÇÕES NECESSÁRIAS

### Environment Variables

```env
# Backend
QI_DAY_DUE=3
REDIS_URL=redis://localhost:6379
BILLING_SCHEDULER_ENABLED=true
SUBSCRIPTION_SCHEDULER_ENABLED=true

# Jobs
BULL_REDIS_HOST=localhost
BULL_REDIS_PORT=6379
BULL_REDIS_PASSWORD=
```

### Database Migrations

```sql
-- Adicionar campos para agendamento
ALTER TABLE subscriptions ADD COLUMN scheduled_action VARCHAR(50);
ALTER TABLE subscriptions ADD COLUMN scheduled_date TIMESTAMP;
ALTER TABLE subscriptions ADD COLUMN metadata JSONB;

-- Índices para performance
CREATE INDEX idx_subscriptions_scheduled_date ON subscriptions(scheduled_date);
CREATE INDEX idx_subscriptions_next_billing ON subscriptions(next_billing_date);
```

## ESTIMATIVA DE ESFORÇO

- **Alta Prioridade**: 3-4 sprints (24-32 story points)
- **Média Prioridade**: 2-3 sprints (16-24 story points)
- **Baixa Prioridade**: 1-2 sprints (8-16 story points)
- **Total**: 6-9 sprints (48-72 story points)

## RISCOS E MITIGAÇÕES

### 🔴 ALTO RISCO

- **Migração de dados existentes**: Assinaturas ativas podem ser afetadas
  - _Mitigação_: Implementar migração gradual com rollback
- **Mudança de comportamento**: Usuários podem estranhar novas regras
  - _Mitigação_: Comunicação prévia e período de adaptação

### 🟡 MÉDIO RISCO

- **Performance**: Jobs de agendamento podem impactar performance
  - _Mitigação_: Usar Redis e otimizar queries
- **Complexidade**: Muitas regras podem gerar bugs
  - _Mitigação_: Testes abrangentes e code review rigoroso

### 🟢 BAIXO RISCO

- **Compatibilidade**: Mudanças podem quebrar integrações
  - _Mitigação_: Manter APIs backward compatible

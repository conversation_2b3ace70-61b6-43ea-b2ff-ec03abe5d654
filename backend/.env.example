RESPONSE_UTIL_LOG_ENABLED=NONE # NONE (default), DEBUG, INFO
RESPONSE_UTIL_LOG_DEPTH= # 3 (default), 1, 2, 3, 4, 5

FIREBASE_API_KEY=
FIREBASE_AUTH_DOMAIN=
FIREBASE_PROJECT_ID=
FIREBASE_STORAGE_BUCKET=
FIREBASE_MESSAGING_SENDER_ID=
FIREBASE_APP_ID=
FIREBASE_SERVICE_ACCOUNT=

# APP & CHECKOUT
CHECKOUT_URL=
APP_URL=

# PAGARME
PAGARME_RECIPIENT_ID= # QIPLUS RECIPIENT ID
PAGARME_API_URL=https://api.pagar.me/core/v5
PAGARME_SECRET_KEY=

# Define se o recipient paga a taxa de processamento da transação.
AFFILIATE_PROCESSING_FEE=true
# Define se o recipient cobre qualquer taxa remanescente que não foi coberta por outro recipient.
AFFILIATE_REMAINDER_FEE=true
# Define se o recipient é responsável por chargebacks e disputas.
AFFILIATE_LIABLE=true

# OPENPIX
OPENPIX_API_KEY=
OPENPIX_BASE_URL=https://api.openpix.com.br/api/v1
OPENPIX_WEBHOOK_URL=https://devs.checkout.api.qi.plus/webhooks/openpix
OPENPIX_WEBHOOK_SECRET=
# Define o prazo para pagamento em dias e define o dia de geração da cobrança
# (dia da assinatura - 3 dias = dia de geração da cobrança)
# exceto quando o dia da assinatura é entre o dia 1 e 3, nesse caso o dia de geração da cobrança será o dia 1
OPENPIX_DAY_DUE=3

# MAILGUN
MAILGUN_API_KEY=
MAILGUN_DELIVERY_IN_DEV=

# ENVIRONMENT
# NODE_ENV=production

# SERVICE AUTHENTICATION
# Token used for service-to-service communication
SERVICE_AUTH_TOKEN=

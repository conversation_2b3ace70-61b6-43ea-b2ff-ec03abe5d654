# Precisamos ajustar novamente a regra para o seguinte:

## Nova asssinatura:

    - Planos mensais sempre serão 1 parcela independente do método
    - Planos anuais pode parcelar em todos os métodos

    ## **Importante**
     Na criação dos pedidos deverá ser da seguinte forma:
     Cartão: 1 pedido com todas as parcelas
     Boleto: 1 pedido por parcela
     Pix: 1 pedido por parcela

## Upgrade de plano:

    - Planos mensais sempre serão 1 parcela independente do método
    - Planos anuais pode parcelar em todos os métodos

    ## **Importante**
     Na criação dos pedidos deverá ser da seguinte forma:
     Cartão: 1 pedido com todas as parcelas
     Boleto: 1 pedido por parcela
     Pix: 1 pedido por parcela

    ## Regras de cobrança:
     - Primeira parcela: valor da diferença calculada (upgradeValues.immediateCharge)
     - Parcelas subsequentes: valor cheio do novo plano (newPlan.yearlyPrice ou newPlan.monthlyPrice)
     - Nova assinatura encerra na mesma data da assinatura anterior

# Exemplos de upgrade:

## 1. Plano A mensal 100 para Plano B mensal 200 no dia 15 de 30 (plano diferente, mesma recorrência)

- Crédito: 100 / 30 \* 15 = 50
- Novo valor proporcional: 200 / 30 \* 15 = 100
- Valor a ser cobrado: 100 - 50 = 50
- Valor do pedido: 50 (não gera outros pedidos)

## 2. Plano A mensal 100 para plano A anual 1200 (mesmo plano)

- Agenda a atual para cancelar no fim do periodo atual
- Cria uma nova assinatura anual agendada para ativar no fim do periodo da atual

## 3. Plano A mensal 100 para Plano B anual 1200

- Não é permitido

## 4. Plano anual 1200 para plano anual de 2400 no dia 30 de 90 (nova assinatura segue a quantidade de parcelas restantes da atual)

## installments (3) - cycle (1) = 2

- Crédito: 1200 / 90 \* 60 = 800 (mesmo que o cálculo do mês anterior)
- Novo valor proporcional: 2400 / 90 \* 60 = 1600
- Valor a ser cobrado: 1600 - 800 = 800
- Valor do 1º pedido: 800
- Gera 2 pedidos com o valor cheio do novo plano (2400 / 3 = 800)

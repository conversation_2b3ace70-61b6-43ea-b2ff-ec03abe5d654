{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"src/*": ["src/*"], "test/*": ["test/*"], "modules/*": ["src/modules/*"], "utils/*": ["src/utils/*"], "types/*": ["src/types/*"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "esModuleInterop": true, "strict": true, "strictPropertyInitialization": false}}
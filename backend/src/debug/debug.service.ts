import { Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
@Injectable()
export class DebugService {
  private readonly api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: 'https://webhook.site/26839770-1e34-455b-aa4c-0e0519257aac',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async sendWebhook(body: any) {
    await this.api.post('', body);
  }
}

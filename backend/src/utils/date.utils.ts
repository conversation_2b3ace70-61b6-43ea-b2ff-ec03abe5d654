// utils/DateUtils.ts - BACKEND (NestJS)
import { BadRequestException } from '@nestjs/common';
import { FieldValue, Timestamp } from 'firebase-admin/firestore';

// Constante para validação de timezone (24 horas em milissegundos)
export const MAX_TIMEZONE_DIFF = 24 * 60 * 60 * 1000;
export const MONTHS = {
  JAN: 0,
  FEB: 1,
  MAR: 2,
  APR: 3,
  MAY: 4,
  JUN: 5,
  JUL: 6,
  AUG: 7,
  SEP: 8,
  OCT: 9,
  NOV: 10,
  DEC: 11,
};

export function convertFirebaseTimestamp(date: { _seconds: number; _nanoseconds: number }) {
  return new Date(date._seconds * 1000 + Math.floor(date._nanoseconds / 1000000));
}

/**
 * Valida se um timestamp Firebase está em um formato válido (não corrompido)
 * @param timestamp - objeto timestamp do Firebase
 * @returns true se o timestamp é válido
 */
export function isValidFirebaseTimestamp(timestamp: { _seconds: number; _nanoseconds: number }): boolean {
  // Timestamps válidos devem ter _seconds > 1000000000 (aproximadamente 2001)
  // e < 4000000000 (aproximadamente 2096)
  return timestamp._seconds > 1000000000 && timestamp._seconds < 4000000000;
}

/**
 * Corrige timestamps Firebase corrompidos usando a data atual como fallback
 * @param timestamp - objeto timestamp do Firebase
 * @returns timestamp corrigido ou atual se inválido
 */
export function sanitizeFirebaseTimestamp(timestamp: { _seconds: number; _nanoseconds: number }): { _seconds: number; _nanoseconds: number } {
  if (isValidFirebaseTimestamp(timestamp)) {
    return timestamp;
  }

  // Se o timestamp está corrompido, usar a data atual
  const now = new Date();
  return {
    _seconds: Math.floor(now.getTime() / 1000),
    _nanoseconds: (now.getTime() % 1000) * 1000000
  };
}

/**
 * Obtém o timestamp atual em milissegundos
 * @returns timestamp atual como number
 */
export function currentTimestamp(currentDateOverride?: Date): number {
  return nowUTCDate(currentDateOverride).getTime();
}

/**
 * Obtém a data atual em UTC
 * @returns Date object em UTC
 */
export function nowUTCDate(currentDateOverride?: Date): Date {
  if (!currentDateOverride) {
    return new Date();
  }
  return new Date(currentDateOverride);
}

/**
 * Adiciona dias a uma data
 * @param date - data base
 * @param days - número de dias a adicionar
 * @returns nova data com os dias adicionados
 */
export function addDays(timestamp: number, days: number): number {
  return timestamp + (days * 24 * 60 * 60 * 1000);
}

/**
 * Adiciona meses a uma data
 * @param date - data base
 * @param months - número de meses a adicionar
 * @returns nova data com os meses adicionados
 */
export function addMonths(timestamp: number, months: number): number {
  const newDate = new Date(timestamp);
  newDate.setMonth(newDate.getMonth() + months);
  return newDate.getTime();
}

/**
 * Adiciona anos a uma data
 * @param date - data base
 * @param years - número de anos a adicionar
 * @returns nova data com os anos adicionados
 */
export function addYears(timestamp: number | Date, years: number): number {
  if (timestamp instanceof Date) {
    timestamp = timestamp.getTime();
  }
  const newDate = new Date(timestamp);
  newDate.setFullYear(newDate.getFullYear() + years);
  return newDate.getTime();
}

/**
 * Cria um FieldValue.serverTimestamp() para o Firebase
 * Use este método ao CRIAR novos documentos
 * @returns FieldValue para timestamp do servidor
 */
export function serverTimestamp(): typeof FieldValue.serverTimestamp {
  return FieldValue.serverTimestamp;
}

/**
 * Cria um Timestamp do Firebase com o momento atual
 * Use este método ao ATUALIZAR documentos existentes
 * @returns Timestamp do Firebase
 */
export function firebaseTimestamp(): Timestamp {
  return Timestamp.now();
}

/**
 * Converte Timestamp do Firebase para number
 * @param firebaseTimestamp - Timestamp do Firebase ou objeto serializado
 * @returns timestamp em milissegundos
 */
export function fromFirebaseTimestamp(firebaseTimestamp: number | Timestamp | { _seconds: number; _nanoseconds: number }): number {

  if (firebaseTimestamp && typeof firebaseTimestamp === 'number') {
    if (`${firebaseTimestamp}`.length === 10) return firebaseTimestamp * 1000;
    return firebaseTimestamp;
  }

  // Se é um objeto serializado do Firebase
  if (firebaseTimestamp && typeof firebaseTimestamp === 'object' && '_seconds' in firebaseTimestamp) {
    return convertFirebaseTimestamp(firebaseTimestamp).getTime();
  }

  // Se é uma instância do Timestamp do Firebase
  if (firebaseTimestamp && typeof firebaseTimestamp === 'object' && 'toMillis' in firebaseTimestamp) {
    return firebaseTimestamp.toMillis();
  }

  throw new Error('Invalid Firebase timestamp format');
}

/**
 * Converte number para Timestamp do Firebase
 * @param timestamp - timestamp em milissegundos
 * @returns Timestamp do Firebase
 */
export function toFirebaseTimestamp(timestamp: number | Date | string): Timestamp {
  if (timestamp instanceof Date) {
    return Timestamp.fromDate(timestamp);
  }
  if (typeof timestamp === 'string') {
    return Timestamp.fromDate(new Date(timestamp));
  }
  return Timestamp.fromMillis(timestamp);
}

/**
 * Valida se um timestamp está dentro do range aceitável
 * Evita que usuários manipulem datas para obter vantagens
 * @param clientTimestamp - timestamp enviado pelo cliente
 * @param maxDiffHours - diferença máxima permitida em horas (padrão: 24h)
 * @returns true se válido
 * @throws BadRequestException se inválido
 */
export function validateClientTimestamp(
  clientTimestamp: number,
  maxDiffHours: number = 24
): boolean {
  const now = currentTimestamp();
  const maxDiff = maxDiffHours * 60 * 60 * 1000;
  const diff = Math.abs(now - clientTimestamp);

  if (diff > maxDiff) {
    throw new BadRequestException(
      `Timestamp inválido. Diferença máxima permitida: ${maxDiffHours}h. ` +
      `Diferença atual: ${Math.round(diff / (60 * 60 * 1000))}h`
    );
  }

  return true;
}

/**
 * Valida timestamp sem lançar exceção
 * @param clientTimestamp - timestamp enviado pelo cliente
 * @param maxDiffHours - diferença máxima permitida em horas (padrão: 24h)
 * @returns objeto com resultado da validação
 */
export function isValidClientTimestamp(
  clientTimestamp: number,
  maxDiffHours: number = 24
): { isValid: boolean; diffHours: number; message?: string } {
  const now = currentTimestamp();
  const maxDiff = maxDiffHours * 60 * 60 * 1000;
  const diff = Math.abs(now - clientTimestamp);
  const diffHours = diff / (60 * 60 * 1000);

  if (diff > maxDiff) {
    return {
      isValid: false,
      diffHours,
      message: `Diferença de ${Math.round(diffHours)}h excede o limite de ${maxDiffHours}h`
    };
  }

  return { isValid: true, diffHours };
}

/**
 * Calcula diferença entre dois timestamps em milissegundos
 * @param timestamp1 - primeiro timestamp
 * @param timestamp2 - segundo timestamp
 * @returns diferença em milissegundos
 */
export function diff(timestamp1: number, timestamp2: number): number {
  return Math.abs(timestamp1 - timestamp2);
}

/**
 * Calcula diferença entre dois timestamps em horas
 * @param timestamp1 - primeiro timestamp
 * @param timestamp2 - segundo timestamp
 * @returns diferença em horas
 */
export function diffInHours(timestamp1: number, timestamp2: number): number {
  return diff(timestamp1, timestamp2) / (60 * 60 * 1000);
}

/**
 * Calcula diferença entre dois timestamps em dias
 * @param timestamp1 - primeiro timestamp
 * @param timestamp2 - segundo timestamp
 * @returns diferença em dias
 */
export function diffInDays(timestamp1: number, timestamp2: number): number {
  return diff(timestamp1, timestamp2) / (24 * 60 * 60 * 1000);
}

/**
 * Verifica se um timestamp é mais antigo que X horas
 * @param timestamp - timestamp a verificar
 * @param hours - número de horas
 * @returns true se mais antigo
 */
export function isOlderThan(timestamp: number, hours: number): boolean {
  const now = currentTimestamp();
  const diff = now - timestamp;
  return diff > (hours * 60 * 60 * 1000);
}

/**
 * Verifica se um timestamp é mais recente que X horas
 * @param timestamp - timestamp a verificar
 * @param hours - número de horas
 * @returns true se mais recente
 */
export function isNewerThan(timestamp: number, hours: number): boolean {
  return !isOlderThan(timestamp, hours);
}

/**
 * Obtém início do dia para um timestamp em UTC
 * @param timestamp - timestamp em milissegundos
 * @returns timestamp do início do dia (00:00:00 UTC)
 */
export function startOfDayUTC(timestamp: number): number {
  const date = new Date(timestamp);
  return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate());
}

/**
 * Obtém fim do dia para um timestamp em UTC
 * @param timestamp - timestamp em milissegundos
 * @returns timestamp do fim do dia (23:59:59.999 UTC)
 */
export function endOfDayUTC(timestamp: number): number {
  const startOfDay = startOfDayUTC(timestamp);
  return startOfDay + (24 * 60 * 60 * 1000) - 1;
}

/**
 * Cria timestamp para uma data específica em UTC
 * @param year - ano
 * @param month - mês (1-12)
 * @param day - dia
 * @param hour - hora (opcional, padrão 0)
 * @param minute - minuto (opcional, padrão 0)
 * @returns timestamp em milissegundos
 */
export function createUTC(year: number, month: number, day: number, hour: number = 0, minute: number = 0): number {
  return Date.UTC(year, month - 1, day, hour, minute);
}

/**
 * Formata timestamp para log (ISO string)
 * @param timestamp - timestamp em milissegundos
 * @returns string ISO formatada
 */
export function formatForLog(timestamp: number | Timestamp | { _seconds: number; _nanoseconds: number }): string {
  if (timestamp && typeof timestamp === 'object' && '_seconds' in timestamp) {
    return convertFirebaseTimestamp(timestamp).toISOString();
  }
  if (timestamp && typeof timestamp === 'object' && 'toMillis' in timestamp) {
    return timestamp.toDate().toISOString();
  }

  return new Date(timestamp).toISOString();
}

/**
 * Formata timestamp para uso em emails
 * Retorna data/hora em formato brasileiro legível
 * @param timestamp - timestamp em milissegundos
 * @param includeTime - se deve incluir a hora (padrão: true)
 * @returns string formatada para email (ex: "15 de março de 2024 às 14:30" ou "15 de março de 2024")
 */
export function formatForEmail(timestamp: number, includeTime: boolean = true): string {
  const date = new Date(timestamp);

  const months = [
    'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
    'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
  ];

  const day = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();

  if (!includeTime) {
    return `${day} de ${month} de ${year}`;
  }

  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${day} de ${month} de ${year} às ${hours}:${minutes}`;
}

/**
 * Formata timestamp para uso em emails em formato mais compacto
 * @param timestamp - timestamp em milissegundos
 * @returns string formatada compacta (ex: "15/03/2024 14:30")
 */
export function formatForEmailCompact(timestamp: number): string {
  const date = new Date(timestamp);

  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${day}/${month}/${year} ${hours}:${minutes}`;
}

/**
 * Formata timestamp para período de tempo em emails
 * @param startTimestamp - timestamp de início
 * @param endTimestamp - timestamp de fim
 * @returns string formatada para período (ex: "15 de março de 2024 das 14:30 às 16:00")
 */
export function formatPeriodForEmail(startTimestamp: number, endTimestamp: number): string {
  const startDate = new Date(startTimestamp);
  const endDate = new Date(endTimestamp);

  const months = [
    'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
    'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
  ];

  const startDay = startDate.getDate();
  const startMonth = months[startDate.getMonth()];
  const startYear = startDate.getFullYear();
  const startHours = startDate.getHours().toString().padStart(2, '0');
  const startMinutes = startDate.getMinutes().toString().padStart(2, '0');

  const endHours = endDate.getHours().toString().padStart(2, '0');
  const endMinutes = endDate.getMinutes().toString().padStart(2, '0');

  // Se for o mesmo dia
  if (startDate.toDateString() === endDate.toDateString()) {
    return `${startDay} de ${startMonth} de ${startYear} das ${startHours}:${startMinutes} às ${endHours}:${endMinutes}`;
  }

  // Se for dias diferentes
  const endDay = endDate.getDate();
  const endMonth = months[endDate.getMonth()];
  const endYear = endDate.getFullYear();

  return `${startDay} de ${startMonth} de ${startYear} às ${startHours}:${startMinutes} até ${endDay} de ${endMonth} de ${endYear} às ${endHours}:${endMinutes}`;
}

/**
 * Obtém dia do mês a partir de um timestamp
 * @param timestamp - timestamp em milissegundos
 * @returns dia do mês (1-31)
 */
export function getDate(timestamp?: number): number {
  timestamp ??= currentTimestamp();
  return new Date(timestamp).getDate();
}

/**
 * Obtém mês a partir de um timestamp
 * @param timestamp - timestamp em milissegundos
 * @returns mês (0-11, onde 0 é janeiro)
 */
export function getMonth(timestamp: number): number {
  return new Date(timestamp).getMonth();
}

/**
 * Obtém ano a partir de um timestamp
 * @param timestamp - timestamp em milissegundos
 * @returns ano (ex: 2024)
 */
export function getFullYear(timestamp: number): number {
  return new Date(timestamp).getFullYear();
}

/**
 * Calcula diferença em dias entre duas datas
 * @param startTimestamp - timestamp de início
 * @param endTimestamp - timestamp de fim
 * @returns diferença em dias (arredondado para cima)
 */
export function calculateDaysUntilBillingSimple(today: number, billingDay: number): number {
  const currentDay = getDate(today);

  if (billingDay === currentDay) {
    return 0;
  }

  // Se o vencimento ainda não chegou este mês
  if (billingDay > currentDay) {
    return billingDay - currentDay;
  }

  // Se já passou, calcular para próximo mês
  // Adicionar 1 mês ao timestamp atual
  const nextMonth = addMonths(today, 1);

  // Criar data do vencimento no próximo mês
  const nextYear = getFullYear(nextMonth);
  const nextMonthNumber = getMonth(nextMonth) + 1; // +1 porque createUTC espera 1-12
  const nextBillingTimestamp = createUTC(nextYear, nextMonthNumber, billingDay);

  // Retornar diferença em dias (arredondado para cima)
  return Math.ceil(diffInDays(nextBillingTimestamp, today));
}


// Exemplo de uso em um Service:
/*
@Injectable()
export class EventService {
  constructor(private firestore: FirebaseFirestore) {}

  async createEvent(createEventDto: CreateEventDto) {
    // Validar timestamp do cliente
    DateUtils.validateClientTimestamp(createEventDto.scheduledFor);

    const eventData = {
      title: createEventDto.title,
      scheduledFor: DateUtils.toFirebaseTimestamp(createEventDto.scheduledFor),
      createdAt: DateUtils.serverTimestamp(), // Para novos documentos
      updatedAt: DateUtils.serverTimestamp()
    };

    return await firestore.collection('events').add(eventData);
  }

  async updateEvent(id: string, updateEventDto: UpdateEventDto) {
    // Validar se existe timestamp para atualizar
    if (updateEventDto.scheduledFor) {
      DateUtils.validateClientTimestamp(updateEventDto.scheduledFor);
    }

    const updateData = {
      ...updateEventDto,
      scheduledFor: updateEventDto.scheduledFor 
        ? DateUtils.toFirebaseTimestamp(updateEventDto.scheduledFor)
        : undefined,
      updatedAt: DateUtils.firebaseTimestamp() // Para atualizações
    };

    return await firestore.collection('events').doc(id).update(updateData);
  }

  async getEvents(): Promise<EventResponseDto[]> {
    const snapshot = await firestore.collection('events').get();
    
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title,
        scheduledFor: DateUtils.fromFirebaseTimestamp(data.scheduledFor), // Converter para number
        createdAt: DateUtils.fromFirebaseTimestamp(data.createdAt),
        updatedAt: DateUtils.fromFirebaseTimestamp(data.updatedAt)
      };
    });
  }
}

@Injectable()
export class EmailService {
  async sendEventNotification(event: Event, user: User) {
    const eventDate = DateUtils.formatForEmail(event.scheduledFor);
    const createdAt = DateUtils.formatForEmailCompact(event.createdAt);
    
    const emailContent = `
      Olá ${user.name},
      
      Seu evento "${event.title}" está agendado para ${eventDate}.
      
      Criado em: ${createdAt}
      
      Atenciosamente,
      Equipe
    `;
    
    // Enviar email...
  }
  
  async sendMeetingInvite(meeting: Meeting) {
    const period = DateUtils.formatPeriodForEmail(meeting.startTime, meeting.endTime);
    
    const emailContent = `
      Você foi convidado para a reunião "${meeting.title}".
      
      Período: ${period}
      
      Local: ${meeting.location}
    `;
    
    // Enviar email...
  }
}
*/
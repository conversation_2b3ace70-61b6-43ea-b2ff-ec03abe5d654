import { Plan } from 'src/modules/plan/model/plan.model';
import { PlanMetadata } from 'src/modules/plan/types/metadata.type';
import { getPlanOption } from './plan.utils';

export interface AccountConfiguration {
  config: Record<string, number | string | boolean | string[]>;
  modules: Record<string, boolean>;
  data: any;
}

export const updateAccountFromMetadata = (
  plan: Plan,
  metadata: PlanMetadata,
): Partial<Plan> => {
  const modules = {
    ...plan.modules,
  };
  for (const additional of metadata.additionals.split(',')) {
    modules[additional.replace('-module', '')] = true;
  }

  const config = {
    ...plan.config,
  };
  for (const feature of metadata.features.split(',')) {
    const [id, quantity] = feature.split(':');
    config[id] = parseInt(quantity);
  }

  return {
    config,
    modules,
    option: getPlanOption(plan, plan.leadsCount),
  };
};

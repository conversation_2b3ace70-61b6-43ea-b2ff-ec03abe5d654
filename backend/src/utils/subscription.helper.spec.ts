import { QISubscriptionItem } from 'src/modules/core/types';
import { calculateYearlyBillingDate, calculateYearlyInstallmentsAmount } from './subscription.helper';
import { PaymentMethod } from 'src/modules/payment/enum/paymentMethod.enum';
import { MONTHS } from './date.utils';

describe('Subscription Helper', () => {
  describe('calculateYearlyBillingDate', () => {
    it('should return the current month when billing day is in the future', () => {
      const billingDay = 15;

      // jan 10, 2025 (billing day 15 is in the future)
      const currentDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime();
      // jan 15, 2025 (same month)
      const expectedDate = new Date(2025, MONTHS.JAN, 15, 0, 0, 0, 0).getTime();

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });

    it('should return the next month when billing day is in the past', () => {
      const billingDay = 5;

      // jan 10, 2025 (billing day 5 is in the past)
      const currentDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime();
      // feb 5, 2025 (next month)
      const expectedDate = new Date(2025, MONTHS.FEB, 5, 0, 0, 0, 0).getTime();

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });

    it('should return the current month when billing day is the same as current date', () => {
      const billingDay = 10;

      // jan 10, 2025 (billing day is today)
      const currentDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime();
      // jan 10, 2025 (same month)
      const expectedDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime();

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });
  });

  describe('calculateFirstInstallmentAmount', () => {
    it('should calculate the first installment amount correctly when billing day is in the future', () => {
      const items: QISubscriptionItem[] = [
        { totalPrice: 200 },
        { totalPrice: 300 },
        { totalPrice: 500 },
      ] as QISubscriptionItem[];
      const installments = 10;
      const billingDay = 15;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount({
        items,
        installments,
        billingDay,
        creditDiscount: 0,
        endDate: new Date(2026, MONTHS.JAN, 1, 0, 0, 0, 0).getTime(),
        paymentMethod: PaymentMethod.PIX,
        today: new Date(2025, MONTHS.JAN, 1, 0, 0, 0, 0).getTime(),
      });

      expect(firstInstallmentAmount).toBe(38);
      expect(remainingInstallmentAmount).toBe(100);
    });

    it('should calculate the first installment amount correctly when billing day is in the past', () => {
      const items = [
        { totalPrice: 200 },
        { totalPrice: 300 },
        { totalPrice: 500 },
      ] as QISubscriptionItem[];
      const installments = 10;
      const billingDay = 5;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount({
        items,
        installments,
        billingDay,
        creditDiscount: 0,
        paymentMethod: PaymentMethod.PIX,
        today: new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime(),
        endDate: new Date(2026, MONTHS.JAN, 10, 0, 0, 0, 0).getTime(),
      });

      expect(firstInstallmentAmount).toBe(71);
      expect(remainingInstallmentAmount).toBe(100);
    });

    it('should calculate the first installment amount correctly when billing day is the same as current date', () => {
      const items = [
        { totalPrice: 200 },
        { totalPrice: 300 },
        { totalPrice: 500 },
      ] as QISubscriptionItem[];
      const installments = 10;
      const billingDay = 10;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount({
        items,
        installments,
        billingDay,
        creditDiscount: 0,
        paymentMethod: PaymentMethod.PIX,
        today: new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime(),
        endDate: new Date(2026, MONTHS.JAN, 10, 0, 0, 0, 0).getTime(),
      });

      expect(firstInstallmentAmount).toBe(100);
      expect(remainingInstallmentAmount).toBe(100);
    });

    it('CalculateYearlyInstallmentsAmount', () => {
      const items: QISubscriptionItem[] = [
        {
          id: 'plan',
          name: 'Digital Starter',
          type: 'plan',
          included: 0,
          quantity: 1,
          unitPrice: 83880,
          totalPrice: 83880
        },
        {
          id: 'shotx-module',
          name: 'Shotx',
          type: 'module',
          included: 0,
          quantity: 1,
          unitPrice: 107880,
          totalPrice: 107880
        },
        {
          id: 'funnels_included',
          type: 'addon',
          name: 'Funis de Vendas',
          included: 1,
          quantity: 1,
          unitPrice: 58800,
          totalPrice: 0
        },
        {
          id: 'landing-pages_included',
          type: 'addon',
          name: 'Landing Pages',
          included: 2,
          quantity: 2,
          unitPrice: 58800,
          totalPrice: 0
        }
      ];
      const installments = 12;
      const billingDay = 20;
      const creditDiscount = 0;
      const endDate = new Date(2026, MONTHS.MAY, 20, 2, 42, 0, 0).getTime(); //30520894920; // 20/05/2026 2:25 AM UTC;
      const today = new Date(2025, MONTHS.JUN, 12, 2, 42, 0, 0).getTime(); //1749694920 // 12/06/2025 2:25 AM UTC;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount({
        items: items,
        installments,
        billingDay,
        creditDiscount,
        endDate,
        paymentMethod: PaymentMethod.PIX,
        today,
      });

      expect(firstInstallmentAmount).toBe(4486);
      expect(remainingInstallmentAmount).toBe(15980);
    });
  });

  describe('calculateYearlyBillingToUpgrade', () => {
    it('Upgrade from yearly Starter to yearly Sinapses', () => {
      // Fazendo o upgrade de Digital Starter para Digital Sinapses
      // *valor cobrado é proporcional
      // Atual 1x 102,92 + 11x 159,80 = 1860,72
      // De 26/06/2025 - 15/06/2026, em 12x vencendo dia 15

      // Novo plano
      const items: QISubscriptionItem[] = [
        { totalPrice: 189480 },
        { totalPrice: 107880 },
      ] as QISubscriptionItem[];
      const installments = 12;
      const billingDay = 15;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount({
        items,
        installments,
        billingDay,
        creditDiscount: 184447,
        paymentMethod: PaymentMethod.PIX,
        today: new Date('2025-06-29T12:37:49.369Z').getTime(),
        endDate: new Date('2026-06-15T17:22:50.513Z').getTime(),
      });

      expect(firstInstallmentAmount).toBe(0);
      expect(remainingInstallmentAmount).toBe(9241);
    });
  });
});


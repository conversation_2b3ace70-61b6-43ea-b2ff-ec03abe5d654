import { DefaultResponse } from 'src/modules/firebase/types/response.type';
import util from 'util';

export class ResponseUtil {
  static inspect(log: any) {
    return util.inspect(log, {
      showHidden: false,
      depth: process.env.RESPONSE_UTIL_LOG_DEPTH
        ? parseInt(process.env.RESPONSE_UTIL_LOG_DEPTH)
        : 3,
      colors: true,
    });
  }
  static defaultResponse<T>(
    error: boolean,
    message: string,
    data: T,
  ): DefaultResponse<T> {
    switch (process.env.RESPONSE_UTIL_LOG_ENABLED) {
      case 'DEBUG':
        console.debug('ResponseUtil:', this.inspect({ error, message, data }));
        break;
      case 'INFO':
        console.info(
          'ResponseUtil:',
          this.inspect(`${error ? 'Error' : 'Success'}: ${message}`),
        );
        break;
    }
    return { error, message, data };
  }

  static error(message: string, error?: any): DefaultResponse<null> {
    return this.defaultResponse(true, message, error);
  }

  static success<T>(message: string, data: T): DefaultResponse<T> {
    return this.defaultResponse<T>(false, message, data);
  }
}

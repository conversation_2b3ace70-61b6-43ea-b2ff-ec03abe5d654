import * as crypto from 'crypto';

export const hmacVerifySignature = (
  secretKey: string,
  payload: string,
  signature?: string,
): boolean => {
  if (!signature) {
    return false;
  }

  try {
    const hmac = crypto.createHmac('sha256', secretKey);
    console.log('hmac', hmac);
    const calculatedSignature = hmac.update(payload).digest('hex');
    console.log('calculatedSignature', calculatedSignature);

    // Remove 'sha256=' prefix if present in the received signature
    const receivedSignature = signature.replace('sha256=', '');
    console.log('receivedSignature', receivedSignature);

    // Compare the signatures directly as hex strings
    return calculatedSignature === receivedSignature;
  } catch (error) {
    console.error('Error verifying HMAC signature:', error);
    return false;
  }
};

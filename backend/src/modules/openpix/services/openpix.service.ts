import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError, AxiosInstance } from 'axios';
import { PaginationDto } from 'src/modules/pagarme/dto/pagination.dto';
import { Gateway } from 'src/modules/payment/entities/gateway.interface';
import { CreateCustomerDto, ListCustomersDto } from '../dtos/customer.dto';
import { OpenPixGatewayType } from '../dtos/openpix.dto';
import {
  OpenPixCustomer,
  OpenPixCustomerList,
  OpenPixCustomerResponse,
} from '../interfaces/customer.interface';

@Injectable()
export class OpenPixService implements Gateway<OpenPixGatewayType> {
  private readonly logger = new Logger(OpenPixService.name);
  private readonly api: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    const baseUrl = this.configService.get<string>('openpix.baseUrl');
    const apiKey = this.configService.get<string>('openpix.apiKey');

    if (!baseUrl || !apiKey) {
      throw new Error('OpenPix configuration is missing');
    }

    this.api = axios.create({
      baseURL: baseUrl,
      headers: {
        Authorization: apiKey,
      },
    });
  }
  listCostumers(pagination?: PaginationDto): Promise<any> {
    throw new Error('Method not implemented.');
  }

  async createCustomer(
    customer: CreateCustomerDto,
  ): Promise<OpenPixCustomerResponse | AxiosError> {
    try {
      const { data } = await this.api.post<OpenPixCustomerResponse>(
        '/customer',
        customer,
      );
      return data;
    } catch (error: any) {
      if (error instanceof AxiosError) {
        this.logger.error(error.response?.data?.error || error.message);
      }
      return error;
    }
  }

  async updateCustomer(
    correlationID: string,
    customer: CreateCustomerDto,
  ): Promise<OpenPixCustomerResponse | AxiosError> {
    try {
      const { data } = await this.api.patch<OpenPixCustomerResponse>(
        `/customer/${correlationID}`,
        customer,
      );
      return data;
    } catch (error: any) {
      if (error instanceof AxiosError) {
        this.logger.error(error.response?.data?.error || error.message);
      }
      return error;
    }
  }

  async getCustomer(correlationID: string): Promise<OpenPixCustomer | null> {
    try {
      const { data } = await this.api.get<OpenPixCustomerResponse>(
        `/customer/${correlationID}`,
      );
      if (data.status === 'OK') {
        return data.customer;
      }
      return null;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(error.response?.data?.error || error.message);
      }
      return null;
    }
  }

  async listCustomers(dto: ListCustomersDto): Promise<OpenPixCustomerList> {
    try {
      const { data } = await this.api.get<OpenPixCustomerList>('/customer', {
        params: dto,
      });
      return data;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error listing customers:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }

  findCustomerDocument(document: string): Promise<any> {
    throw new Error('Method not implemented.');
  }

  findCustomerByUid(uid: string): Promise<any> {
    return this.getCustomer(uid);
  }

  async findCustomerIdByUid(uid: string): Promise<any> {
    const customer = await this.getCustomer(uid);
    return customer?.correlationID;
  }

  listCards(customerId: string, pagination?: PaginationDto): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  createCard(cardData: any): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  deleteCard(customerId: string, cardId: string): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  getCard(customerId: string, cardId: string): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  listSubscriptions(
    customerId: string,
    pagination?: PaginationDto,
  ): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  async createSubscription(subscriptionData: any): Promise<unknown> {
    try {
      const { data } = await this.api.post<OpenPixCustomerResponse>(
        '/subscriptions',
        subscriptionData,
      );
      return data;
    } catch (error: any) {
      if (error instanceof AxiosError) {
        this.logger.error(error.response?.data?.error || error.message);
      }
      return error;
    }
  }
  async getSubscription(globalIdOrCorrelationID: string): Promise<unknown> {
    try {
      const { data } = await this.api.get<OpenPixCustomerResponse>(
        `/subscriptions/sub_${globalIdOrCorrelationID}`,
      );
      return data;
    } catch (error: any) {
      if (error instanceof AxiosError) {
        this.logger.error(error.response?.data?.error || error.message);
      }
      return error;
    }
  }

  updateSubscription(subscriptionData: any): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  cancelSubscription(subscriptionId: string): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  updateSubscriptionMetadata(
    subscriptionId: string,
    metadata: any,
  ): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  enableManualBilling(subscriptionId: string): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  disableManualBilling(subscriptionId: string): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  listInvoices(
    subscriptionId: string,
    pagination?: PaginationDto,
  ): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  getInvoice(invoiceId: string): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  getSubscriptionItems(
    subscriptionId: string,
    pagination?: PaginationDto,
  ): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  getSubscriptionIncrements(
    subscriptionId: string,
    pagination?: PaginationDto,
  ): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  createOrder(orderData: any): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  cancelOrder(orderId: string): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  listRecipients(pagination?: PaginationDto): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  editSplit(subscriptionId: string, split: any): Promise<unknown> {
    throw new Error('Method not implemented.');
  }
  getCharge(chargeId: string): Promise<any> {
    throw new Error('Method not implemented.');
  }
}

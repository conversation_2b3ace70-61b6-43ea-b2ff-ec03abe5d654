import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError, AxiosInstance } from 'axios';
import {
  CreateCustomerDto,
  GetCustomerDto,
  ListCustomersDto,
} from '../dtos/customer.dto';
import {
  OpenPixCustomerList,
  OpenPixCustomerResponse,
} from '../interfaces/customer.interface';

@Injectable()
export class OpenPixCustomerService {
  private readonly logger = new Logger(OpenPixCustomerService.name);
  private readonly api: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    this.api = axios.create({
      baseURL: this.configService.get<string>('openpix.baseUrl'),
      headers: {
        Authorization: this.configService.get<string>('openpix.apiKey'),
      },
    });
  }

  async createCustomer(
    dto: CreateCustomerDto,
  ): Promise<OpenPixCustomerResponse> {
    try {
      const { data } = await this.api.post<OpenPixCustomerResponse>(
        '/customer',
        {
          customer: dto,
        },
      );
      return data;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error creating customer:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }

  async getCustomer(dto: GetCustomerDto): Promise<OpenPixCustomerResponse> {
    try {
      const { data } = await this.api.get<OpenPixCustomerResponse>(
        `/customer/${dto.correlationID}`,
      );
      return data;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error getting customer:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }

  async listCustomers(dto: ListCustomersDto): Promise<OpenPixCustomerList> {
    try {
      const { data } = await this.api.get<OpenPixCustomerList>('/customer', {
        params: dto,
      });
      return data;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error listing customers:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }
}

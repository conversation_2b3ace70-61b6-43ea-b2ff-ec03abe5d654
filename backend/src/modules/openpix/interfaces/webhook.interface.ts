export type OpenPixWebhookEvent =
  | 'OPENPIX:CHARGE_CREATED'
  | 'OPENPIX:CHARGE_COMPLETED'
  | 'OPENPIX:CHARGE_EXPIRED'
  | 'OPENPIX:TRANSACTION_RECEIVED'
  | 'OPENPIX:TRANSACTION_REFUND_RECEIVED';

export interface OpenPixWebhook {
  name: string;
  event: OpenPixWebhookEvent;
  url: string;
  authorization: string;
  isActive: boolean;
}

export interface OpenPixWebhookResponse {
  webhook: OpenPixWebhook & {
    id: string;
    createdAt: string;
    updatedAt: string;
  };
}

export interface OpenPixWebhookPayload {
  charge?: {
    status: 'ACTIVE' | 'COMPLETED' | 'EXPIRED';
    value: number;
    correlationID: string;
    transactionID: string;
    customer?: {
      name: string;
      email: string;
      phone?: string;
      taxID: {
        taxID: string;
        type: 'BR:CPF' | 'BR:CNPJ';
      };
    };
  };
  pix?: {
    charge: {
      correlationID: string;
      value: number;
      status: string;
    };
    time: string;
    value: number;
    transactionID: string;
  };
}

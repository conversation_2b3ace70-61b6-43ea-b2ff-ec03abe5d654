export interface OpenPixSubscription {
  value: number;
  customer: {
    name: string;
    taxID: string;
    email: string;
    phone?: string;
  };
  dayOfMonth: number;
  interval?: 'MONTHLY' | 'YEARLY';
  chargeType?: 'PIX' | 'PAYMENT_LINK';
}

export interface OpenPixSubscriptionResponse {
  subscription: {
    id: string;
    value: number;
    customer: {
      name: string;
      taxID: string;
      email: string;
      phone?: string;
    };
    dayOfMonth: number;
    interval: 'MONTHLY' | 'YEARLY';
    chargeType: 'PIX' | 'PAYMENT_LINK';
    status: 'ACTIVE' | 'INACTIVE' | 'CANCELLED';
    createdAt: string;
    updatedAt: string;
  };
}

export interface OpenPixSubscriptionList {
  subscriptions: OpenPixSubscriptionResponse['subscription'][];
  pageInfo: {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    startCursor: string;
    endCursor: string;
  };
}

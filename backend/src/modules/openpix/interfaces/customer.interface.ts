export interface OpenPixCustomer {
  name: string;
  taxID: {
    taxID: string;
    type: 'BR:CPF' | 'BR:CNPJ';
  };
  email: string;
  phone?: string;
  correlationID: string;
}

export interface OpenPixCustomerResponse {
  status: string;
  customer: OpenPixCustomer;
}

export interface OpenPixCustomerList {
  customers: OpenPixCustomer[];
  pageInfo: {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    startCursor: string;
    endCursor: string;
  };
}

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DebugModule } from 'src/debug/debug.module';
import openPixConfig from './openpix.config';
import { OpenPixCustomerService } from './services/customer.service';
import { OpenPixService } from './services/openpix.service';
import { OpenPixSubscriptionService } from './services/subscription.service';
import { OpenPixWebhookService } from './services/webhook.service';
@Module({
  imports: [ConfigModule.forFeature(openPixConfig), DebugModule],
  controllers: [],
  providers: [
    OpenPixService,
    OpenPixCustomerService,
    OpenPixSubscriptionService,
    OpenPixWebhookService,
  ],
  exports: [
    OpenPixService,
    OpenPixCustomerService,
    OpenPixSubscriptionService,
    OpenPixWebhookService,
  ],
})
export class OpenPixModule {}

import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsNotEmptyObject,
  IsObject,
  IsString,
} from 'class-validator';

export class ChargeDto {
  @ApiProperty({ description: 'Data de criação da cobrança' })
  @IsString()
  @IsNotEmpty()
  @IsDateString()
  data_criacao: string;

  @ApiProperty({ description: 'Evento da cobrança' })
  @IsString()
  @IsNotEmpty()
  evento: string;

  @ApiProperty({ description: 'Evento da cobrança' })
  @IsString()
  @IsNotEmpty()
  event: string;

  @ApiProperty({ description: 'Dados da cobrança' })
  @IsObject()
  @IsNotEmptyObject()
  charge: object;
}

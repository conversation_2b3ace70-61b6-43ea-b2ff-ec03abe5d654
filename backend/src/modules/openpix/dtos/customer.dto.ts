import { Type } from 'class-transformer';
import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class TaxIDDto {
  @IsString()
  taxID: string;

  @IsEnum(['BR:CPF', 'BR:CNPJ'])
  type: 'BR:CPF' | 'BR:CNPJ';
}

export class AddressDto {
  @IsString()
  zipcode: string;

  @IsString()
  street: string;

  @IsString()
  number: string;

  @IsString()
  neighborhood: string;

  @IsString()
  city: string;

  @IsString()
  state: string;

  @IsString()
  complement: string;

  @IsString()
  country: string;
}

export class CreateCustomerDto {
  @IsString()
  name: string;

  @IsString()
  taxID: string;

  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  correlationID?: string;

  @ValidateNested()
  @Type(() => AddressDto)
  address: AddressDto;
}

export class GetCustomerDto {
  @IsString()
  correlationID: string;
}

export class ListCustomersDto {
  @IsOptional()
  @IsString()
  after?: string;

  @IsOptional()
  @IsString()
  before?: string;

  @IsOptional()
  @IsString()
  first?: string;

  @IsOptional()
  @IsString()
  last?: string;
}

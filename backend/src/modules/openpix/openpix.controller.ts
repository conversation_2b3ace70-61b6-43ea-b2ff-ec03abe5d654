import {
  Body,
  Controller,
  Header,
  Headers,
  HttpCode,
  Logger,
  Post,
} from '@nestjs/common';
import { Api<PERSON>ody, ApiHeader } from '@nestjs/swagger';
import { ChargeDto } from './dtos/charge.dto';
import { OpenPixWebhookService } from './services/webhook.service';
import { WebhookPostBody } from './types/webhook.types';

@Controller('webhooks/openpix')
export class OpenPixController {
  private readonly logger = new Logger(OpenPixController.name);
  private readonly openPixWebhookSecret = process.env.OPENPIX_WEBHOOK_SECRET;

  constructor(private readonly openPixWebhookService: OpenPixWebhookService) {}

  @Post(['created', 'paid', 'expired', 'refunded'])
  @ApiBody({ type: ChargeDto })
  @ApiHeader({ name: 'x-openpix-signature', required: true })
  @HttpCode(200)
  @Header('Content-Type', 'application/json')
  async webhook(
    @Body() body: WebhookPostBody,
    @Headers('x-openpix-signature') signature: string,
  ) {
    this.logger.debug('Webhook received', body);
    return this.openPixWebhookService.handleWebhookPayload(
      body,
      this.openPixWebhookSecret!,
      signature,
    );
  }

  @Post('external_paid')
  @ApiBody({ type: ChargeDto })
  @ApiHeader({ name: 'x-openpix-signature', required: true })
  @HttpCode(200)
  @Header('Content-Type', 'application/json')
  async webhookExternal(
    @Body() body: WebhookPostBody,
    @Headers('x-openpix-signature') signature: string,
  ) {
    this.logger.debug('Webhook received', body);
    return this.openPixWebhookService.handleWebhookPayload(
      body,
      this.openPixWebhookSecret!,
      signature,
    );
  }

  @Post([
    'dispute_created',
    'dispute_rejected',
    'dispute_accepted',
    'dispute_canceled',
  ])
  @ApiBody({ type: ChargeDto })
  @ApiHeader({ name: 'x-openpix-signature', required: true })
  @HttpCode(200)
  @Header('Content-Type', 'application/json')
  async webhookDispute(
    @Body() body: WebhookPostBody,
    @Headers('x-openpix-signature') signature: string,
  ) {
    this.logger.debug('Webhook received', body);
    return this.openPixWebhookService.handleWebhookPayload(
      body,
      this.openPixWebhookSecret!,
      signature,
    );
  }

  @Post('refunded')
  @ApiBody({ type: ChargeDto })
  @ApiHeader({ name: 'x-openpix-signature', required: true })
  @HttpCode(200)
  @Header('Content-Type', 'application/json')
  async webhookRefunded(
    @Body() body: WebhookPostBody,
    @Headers('x-openpix-signature') signature: string,
  ) {
    this.logger.debug('Webhook received', body);
    return this.openPixWebhookService.handleWebhookPayload(
      body,
      this.openPixWebhookSecret!,
      signature,
    );
  }
}

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { AffiliateModule } from 'src/modules/affiliates/affiliate.module';

import { PagarmeModule } from 'src/modules/pagarme/pagarme.module';
import { PlanModule } from 'src/modules/plan/plan.module';

import { PaymentController } from './payment.controller';
@Module({
  controllers: [PaymentController],
  providers: [],
  imports: [
    PagarmeModule,
    PlanModule,
    AffiliateModule,
    ConfigModule,
  ],
  exports: [],
})
export class PaymentModule { }

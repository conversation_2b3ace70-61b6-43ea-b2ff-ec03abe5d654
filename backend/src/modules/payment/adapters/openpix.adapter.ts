import { RegisterDto } from 'src/modules/auth/dto/register.dto';
import { CreateCustomerDto } from 'src/modules/openpix/dtos/customer.dto';
import { SubscriptionRequest } from 'src/modules/pagarme/types/pagarme.type';
import { Plan } from 'src/modules/plan/model/plan.model';
import { QISubscription } from 'src/modules/subscription/types/qiplus.types';
import { Gateway } from 'src/types/gateway.enum';
import { removeKeysEmptyOrUndefinedInObject } from 'src/utils/objetct';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { onlyNumbers } from '../utils/helpers';
import { PaymentAdapter } from './adapters';

export class OpenPixAdapter implements PaymentAdapter {
  toCreateCustomer(
    data: CreatePaymentDto | (RegisterDto & { uid: string }),
  ): CreateCustomerDto {
    if (data instanceof CreatePaymentDto) {
      return {
        name: data.name,
        taxID: onlyNumbers(data.companyCnpj || data.cpf),
        email: data.email,
        phone: onlyNumbers(`${data.phoneCountryCode}${data.phone}`),
        correlationID: data.uid,
        address: {
          zipcode: onlyNumbers(data.postalCode),
          street: data.street,
          number: data.number,
          neighborhood: data.neighborhood,
          city: data.city,
          state: data.state,
          complement: data.complement,
          country: data.country,
        },
      };
    }

    return {
      name: data.name,
      taxID: onlyNumbers(data.company.cnpj || data.cpf),
      email: data.email,
      phone: onlyNumbers(`${data.mobileCC}${data.mobile}`),
      correlationID: data.uid,
      address: {
        zipcode: onlyNumbers(data.address.postalCode),
        street: data.address.street,
        number: data.address.number,
        neighborhood: data.address.neighborhood,
        city: data.address.city,
        state: data.address.state,
        complement: data.address.complement,
        country: data.address.country,
      },
    };
  }

  toUpdateCustomer(data: any) {
    const customer = this.toCreateCustomer(data);
    delete customer.correlationID;
    return customer;
  }

  toGetCustomer(data: any) {
    throw new Error('Method not implemented.');
  }

  fromGetCustomer(data: any) {
    return {
      id: data.correlationID,
      ...data,
    };
  }

  toCard(data: any) {
    throw new Error('Method not implemented.');
  }
  toOrder(data: any) {
    throw new Error('Method not implemented.');
  }

  toSubscription({
    customer,
    plan,
    subscriptionData,
    dayDue,
  }: {
    customer: any;
    plan: Plan;
    subscriptionData: SubscriptionRequest;
    dayDue: string;
  }) {
    const dayDueNumber = parseInt(dayDue);
    const isYearly = plan.isYearly;
    const value = plan.totalValue(isYearly);

    const dayOfMonth = new Date().getDate();
    const dayOfYear = Math.floor(
      (Date.now() - new Date(new Date().getFullYear(), 0, 0).getTime()) /
        1000 /
        60 /
        60 /
        24,
    );

    // TODO: Ver como fazer isso de forma correta para o ano
    let dayGenerateCharge = dayOfMonth;
    dayGenerateCharge =
      dayGenerateCharge > dayDueNumber ? dayGenerateCharge - dayDueNumber : 1;

    const taxID = customer.taxID.taxID;
    delete customer.taxID;
    const metadata = removeKeysEmptyOrUndefinedInObject(
      subscriptionData.metadata,
    );

    const additionalInfo = Object.entries(metadata).map(([key, value]) => ({
      key,
      value: typeof value === 'string' ? value : JSON.stringify(value),
    }));

    return {
      customer: {
        ...customer,
        taxID: taxID,
      },
      value: value * 100,
      frequency: isYearly ? 'ANNUALY' : 'MONTHLY',
      comment: `Assinatura ${plan.name} - ${isYearly ? 'Anual' : 'Mensal'}`,
      chargeType: 'DYNAMIC',
      dayGenerateCharge: dayGenerateCharge,
      dayDue: dayDueNumber,
      additionalInfo,
      correlationID: customer.correlationID,
    };
  }

  toQiSubscription(data: any): Omit<QISubscription, 'id'> {
    return {
      gateway: Gateway.OPENPIX,
      accountId: data.accountId,
      planId: data.planId,
      status: data.status,
      createdAt: data.createdAt,
      billingInterval: data.billingInterval,
      billingDay: data.billingDay,
      startDate: data.startDate,
      endDate: data.endDate,
      cycle: data.cycle,
      installments: data.installments,
      currentPeriodStart: data.currentPeriodStart,
      currentPeriodEnd: data.currentPeriodEnd,
      items: [],
      paymentMethod: data.paymentMethod,
      nextBillingDate: data.nextBillingDate,
      updatedAt: data.updatedAt,
      currency: 'BRL',
      accountConfig: {
        data: {
          contacts_max: 0,
          yearly_value: 0,
          monthly_value: 0,
          contacts_min: 0,
        },
        modules: {
          shotx: false,
        },
        config: {},
      },
    };
  }
}

import { Injectable } from '@nestjs/common';
import { Subscription, QISubscription, QISubscriptionInvoice, QIBillingInterval } from 'src/modules/core/types';
import { Plan } from 'src/modules/plan/model/plan.model';

@Injectable()
export class UpgradeCalculator {
  /**
   * Calcula valores proporcionais para upgrade
   * @param currentSubscription Assinatura atual do cliente
   * @param currentPlan Plano atual
   * @param newPlan Novo plano
   * @param isYearly Se a cobrança é anual
   */
  calculateUpgradeValues(
    currentSubscription: Subscription,
    currentPlan: Plan,
    newPlan: Plan,
    isYearly: boolean,
  ) {
    // 1. Calcular valores totais
    const currentPlanValue = currentPlan.totalValue(currentPlan.isYearly);
    const newPlanValue = newPlan.totalValue(isYearly);

    // 2. Calcular dias restantes no ciclo atual
    const daysRemaining = this.calculateRemainingDays(currentSubscription);
    const totalDaysInCycle =
      this.calculateTotalDaysInCycle(currentSubscription);

    // 3. Calcular valores proporcionais
    const currentPlanCredit =
      (currentPlanValue * daysRemaining) / totalDaysInCycle;
    const newPlanProportional =
      (newPlanValue * daysRemaining) / totalDaysInCycle;

    // 4. Calcular diferença a ser cobrada
    const immediateCharge = Math.max(
      0,
      newPlanProportional - currentPlanCredit,
    );

    return {
      currentPlanCredit,
      newPlanProportional,
      immediateCharge,
      nextBillingValue: newPlanValue,
      daysRemaining,
      currentPlanValue,
      newPlanValue,
    };
  }

  private calculateRemainingDays(subscription: Subscription): number {
    const currentDate = new Date();
    const currentCycleEnd = new Date(subscription.current_cycle.end_at);

    const diffTime = currentCycleEnd.getTime() - currentDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  }

  private calculateTotalDaysInCycle(subscription: Subscription): number {
    const startDate = new Date(subscription.start_at);
    const endDate = new Date(subscription.current_cycle.end_at);

    const diffTime = endDate.getTime() - startDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  }
}

@Injectable()
export class UpgradeCalculatorFromSubscription {
  /**
   * Calcula valores proporcionais para upgrade
   * @param currentSubscription Assinatura atual do cliente
   * @param currentPlan Plano atual
   * @param newPlan Novo plano
   * @param isYearly Se a cobrança é anual
   */
  calculateUpgradeValues(
    currentSubscription: QISubscription & { invoices: QISubscriptionInvoice[] },
    newSubscription: Omit<QISubscription, 'id'>,
  ) {
    // 1. Calcular valores totais da assinatura atual
    const totalPayed = currentSubscription.invoices.reduce(
      (acc, invoice) => acc + invoice.amount,
      0,
    );

    // 2. Calcular o valor do plano atual e novo
    const currentPlanValue = currentSubscription.items.reduce(
      (acc, item) => acc + item.totalPrice,
      0,
    );
    const newPlanValue = newSubscription.items.reduce(
      (acc, item) => acc + item.totalPrice,
      0,
    );

    // 3. Calcular dias restantes no ciclo atual
    const daysRemaining = this.calculateRemainingDays(currentSubscription);
    const totalDaysInCycle =
      this.calculateTotalDaysInCycle(currentSubscription);

    // 4. Calcular o crédito do usuário com base no valor TOTAL PAGO (invoices)
    // Este é o ponto principal: usamos o valor total das faturas pagas
    // e não o valor nominal do plano para calcular o crédito
    const currentPlanCredit = (totalPayed * daysRemaining) / totalDaysInCycle;

    // 5. Calcular o valor proporcional do novo plano
    const newPlanProportional =
      (newPlanValue * daysRemaining) / totalDaysInCycle;

    // 6. Calcular diferença a ser cobrada
    const immediateCharge = Math.max(
      0,
      newPlanProportional - currentPlanCredit,
    );

    return {
      currentPlanCredit,
      newPlanProportional,
      immediateCharge,
      nextBillingValue: newPlanValue,
      daysRemaining,
      currentPlanValue,
      newPlanValue,
      totalPayed, // Incluir o valor total pago para referência
    };
  }

  calculateSubscriptionCredit(
    subscription: QISubscription,
    amountPaid: number,
  ) {
    // 1. Calcular valores totais da assinatura atual
    const totalValue = subscription.items.reduce(
      (acc, item) => acc + item.totalPrice,
      0,
    );

    const dailyRate = this.calculateDailyRate(
      subscription.billingInterval,
      totalValue,
    );

    return this.calculateCycleCredit(
      subscription.startDate.toDate(),
      subscription.currentPeriodEnd.toDate(),
      new Date(),
      totalValue,
      amountPaid,
      dailyRate,
    );
  }

  /**
   * Calculates the available credit for a subscription based on its billing cycle
   * and the total amount already paid.
   *
   * @param cycleStart - Date when the billing cycle started.
   * @param cycleEnd - Date when the billing cycle ends.
   * @param upgradeDate - Date when the user requests the upgrade.
   * @param cycleTotalAmount - Total amount of the cycle (full price of the subscription).
   * @param amountPaid - Total amount paid by the user up to the upgrade date.
   * @returns The available credit amount.
   */
  calculateCycleCredit(
    cycleStart: Date,
    cycleEnd: Date,
    upgradeDate: Date,
    cycleTotalAmount: number,
    amountPaid: number,
    dailyRate: number,
  ): number {
    if (cycleTotalAmount <= 0) {
      return 0; // No credit if the cycle total amount is zero or negative
    }
    if (amountPaid <= 0) {
      return 0; // No credit if the amount paid is negative or zero
    }
    if (upgradeDate < cycleStart) {
      return Math.round(amountPaid); // The entire amount paid can be used as credit if the upgrade date is before the cycle start date
    }

    const totalMsDiff = cycleEnd.getTime() - cycleStart.getTime();
    const usedMsDiff =
      upgradeDate.getTime() - (cycleStart.getTime() - 1000 * 60 * 60 * 24);

    const totalCycleDays = Math.ceil(totalMsDiff / (1000 * 60 * 60 * 24));
    const usedDays = Math.floor(usedMsDiff / (1000 * 60 * 60 * 24));

    if (usedDays >= totalCycleDays) {
      return 0; // The entire cycle has been consumed
    }

    const consumedAmount = usedDays * dailyRate;

    const credit = amountPaid - consumedAmount;

    return Math.round(credit);
  }

  calculateDailyRate(
    billingInterval: QIBillingInterval,
    amountPaid: number,
  ) {

    const daysInCycle = billingInterval === QIBillingInterval.YEARLY ? 365 : 30;
    const dailyRate = amountPaid / daysInCycle;

    return dailyRate;
  }

  private calculateRemainingDays(subscription: QISubscription): number {
    const currentDate = new Date();
    const currentCycleEnd = subscription.currentPeriodEnd.toDate();

    const diffTime = currentCycleEnd.getTime() - currentDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  }

  private calculateTotalDaysInCycle(subscription: QISubscription): number {
    const startDate = subscription.startDate.toDate();
    const endDate = subscription.currentPeriodEnd.toDate();

    const diffTime = endDate.getTime() - startDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  }
}

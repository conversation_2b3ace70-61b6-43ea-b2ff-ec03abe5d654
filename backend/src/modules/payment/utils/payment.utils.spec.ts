import { MONTHS } from "src/utils/date";
import { calculateInstallmentDueDate } from "./payment.utils";

describe('calculateDueDate', () => {
  it('should calculate due date correctly for the first installment', () => {
    const billingDay = 20;
    const currentDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0);
    const dueDate = calculateInstallmentDueDate(1, billingDay, currentDate);
    const expectedDate = new Date(2025, 0, 13, 0, 0, 0, 0);
    expect(dueDate).toBe(expectedDate.toISOString());
  });

  it('should calculate due date correctly for the second installment', () => {
    const billingDay = 20;
    const currentDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0);
    const dueDate = calculateInstallmentDueDate(2, billingDay, currentDate);
    const expectedDate = new Date(2025, MONTHS.JAN, 20, 0, 0, 0, 0);
    expect(dueDate).toBe(expectedDate.toISOString());
  });

  it('should calculate due date correctly for the third installment', () => {
    const billingDay = 20;
    const currentDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0);
    const dueDate = calculateInstallmentDueDate(3, billingDay, currentDate);
    const expectedDate = new Date(2025, MONTHS.FEB, 20, 0, 0, 0, 0);
    expect(dueDate).toBe(expectedDate.toISOString());
  });

  it('should calculate due dates correctly for the twelfth installments', () => {
    const billingDay = 10;
    const currentDate = new Date(2025, MONTHS.MAY, 24, 0, 0, 0, 0);
    const dueDates: string[] = [];
    for (let i = 1; i <= 12; i++) {
      const dueDate = calculateInstallmentDueDate(i, billingDay, currentDate);
      dueDates.push(dueDate);
    }

    expect(dueDates).toEqual([
      new Date(2025, MONTHS.MAY, 27, 0, 0, 0, 0).toISOString(),
      new Date(2025, MONTHS.JUN, 10, 0, 0, 0, 0).toISOString(),
      new Date(2025, MONTHS.JUL, 10, 0, 0, 0, 0).toISOString(),
      new Date(2025, MONTHS.AUG, 10, 0, 0, 0, 0).toISOString(),
      new Date(2025, MONTHS.SEP, 10, 0, 0, 0, 0).toISOString(),
      new Date(2025, MONTHS.OCT, 10, 0, 0, 0, 0).toISOString(),
      new Date(2025, MONTHS.NOV, 10, 0, 0, 0, 0).toISOString(),
      new Date(2025, MONTHS.DEC, 10, 0, 0, 0, 0).toISOString(),
      new Date(2026, MONTHS.JAN, 10, 0, 0, 0, 0).toISOString(),
      new Date(2026, MONTHS.FEB, 10, 0, 0, 0, 0).toISOString(),
      new Date(2026, MONTHS.MAR, 10, 0, 0, 0, 0).toISOString(),
      new Date(2026, MONTHS.APR, 10, 0, 0, 0, 0).toISOString(),
    ]);
  });
});

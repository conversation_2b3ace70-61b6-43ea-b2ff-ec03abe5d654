import { IsNotEmpty, IsString } from 'class-validator';
export class CompanyDto {
  @IsString()
  @IsNotEmpty({ message: 'Nome da empresa é obrigatório' })
  name: string;

  @IsString()
  @IsNotEmpty({ message: 'CNPJ é obrigatório' })
  cnpj: string;

  @IsString()
  @IsNotEmpty({ message: 'Telefone é obrigatório' })
  phone: string;

  @IsString()
  @IsNotEmpty({ message: 'Código de telefone é obrigatório' })
  phoneCountryCode: string;

  @IsString()
  @IsNotEmpty({ message: 'Email é obrigatório' })
  email: string;
}

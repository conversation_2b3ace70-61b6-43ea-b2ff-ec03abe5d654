import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsString,
  <PERSON><PERSON>ength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { AddressDto } from './address.dto';
import { CompanyDto } from './company.dto';
import { FeatureDto } from './feature.dto';

export class RegisterDto {
  @IsEmail({}, { message: 'E-mail inválido' })
  email: string;

  @IsNotEmpty({ message: 'Senha é obrigatória' })
  @MinLength(6, { message: 'A senha deve ter no mínimo 6 caracteres' })
  password: string;

  @IsString()
  @IsNotEmpty({ message: 'Nome é obrigatório' })
  name: string;

  @ValidateIf((o: RegisterDto) => !!o.parentId)
  @IsString()
  parentId?: string;

  @ValidateIf((o: RegisterDto) => !!o.affiliateId)
  @IsString()
  affiliateId?: string;

  @IsString()
  @IsNotEmpty({ message: 'Plano é obrigatório' })
  planId: string;

  @IsString()
  @IsNotEmpty({ message: 'Documento é obrigatório' })
  cpf: string;

  @IsString()
  @IsNotEmpty({ message: 'Data de nascimento é obrigatória' })
  birthdate: string;

  @IsBoolean()
  isCompany: boolean;

  @IsBoolean()
  isYearly: boolean;

  discount: number;

  @IsNumber({}, { message: 'Quantidade de leads deve ser um número' })
  @IsNotEmpty({ message: 'Quantidade de leads é obrigatória' })
  leadsCount: number;

  @IsArray()
  @IsString({ each: true })
  additionals: string[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FeatureDto)
  customs: FeatureDto[];

  @IsString()
  @IsNotEmpty({ message: 'Codigo de area é obrigatório' })
  mobileCC: string;

  @IsString()
  @IsNotEmpty({ message: 'Telefone é obrigatório' })
  mobile: string;

  @ValidateNested()
  @Type(() => AddressDto)
  address: AddressDto;

  @ValidateIf((o: RegisterDto) => o.isCompany === true)
  @Type(() => CompanyDto)
  company: CompanyDto;
}

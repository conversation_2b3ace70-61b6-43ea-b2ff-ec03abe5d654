import {
  Body,
  Controller,
  Post,
  Request,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { FirebaseAuthGuard } from './firebase-auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) { }

  @Post('login')
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuard)
  async login(@Request() req: any) {
    return this.authService.authenticate(req.user);
  }

  @Post('register')
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async register(@Body() dto: RegisterDto) {
    return this.authService.register(dto);
  }


  @Post('getToken')
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuard)
  async getToken(@Request() req: any) {
    return this.authService.getToken(req.user.uid);
  }

}

import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { FirebaseAuthGuard, FirebaseAuthGuardWithAccount, FirebaseAuthGuardWithAccountAndQISubscription } from 'src/modules/auth/firebase-auth.guard';
import { ServiceAuthGuard } from 'src/modules/auth/service-auth.guard';
import { PaginationDto } from '../pagarme/dto/pagination.dto';
import { PagarmeService } from '../pagarme/pagarme.service';
import { SubscriptionService } from './subscription.service';
import { QIScheduledAction, QISubscription, QISubscriptionInvoice, QISubscriptionStatus, QISubscriptionToProcess, QISubscriptionToProcessDto, QISubscriptionToProcessDtoList } from './types/qiplus.types';
import { PaymentService } from '../payment/payment.service';
import Logger from 'src/utils/logger';
import { AccountService } from '../account/account.service';
@ApiTags('Subscriptions')
@Controller('subscriptions')
export class SubscriptionController {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly pagarmeService: PagarmeService,
    private readonly paymentService: PaymentService,
    private readonly accountService: AccountService,
  ) { }

  @ApiOperation({ summary: 'List all subscriptions for a account' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuard)
  @ApiQuery({ name: 'account_id', required: true, description: 'Account ID' })
  @ApiResponse({ status: 200, description: 'Subscriptions found' })
  @Get()
  async getSubscriptions(
    @Query('account_id') accountId: string,
    @Query() pagination: PaginationDto,
  ) {
    return this.subscriptionService.getSubscriptionsByAccountId(accountId);
  }

  @ApiOperation({ summary: 'Get subscription' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuardWithAccountAndQISubscription)
  @ApiParam({ name: 'id', description: 'Subscription ID' })
  @ApiResponse({ status: 200, description: 'Subscription found' })
  @Get('/one/:id')
  async getSubscription(@Param('id') id: string, @Request() req: any) {
    const currentSubscription = req.currentSubscription;
    if (currentSubscription && currentSubscription.id === id) {
      return currentSubscription;
    }
    return this.subscriptionService.getSubscription(id);
  }

  @ApiOperation({ summary: 'Get subscription items' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuard)
  @ApiParam({ name: 'id', description: 'Subscription ID' })
  @ApiResponse({ status: 200, description: 'Subscription items found' })
  @Get(':id/items')
  async getSubscriptionItems(
    @Param('id') id: string,
    @Query() pagination: PaginationDto,
  ) {
    const subscription = await this.subscriptionService.getSubscription(id);
    if (!subscription) {
      return {
        error: true,
        message: 'Subscription not found',
      };
    }
    return subscription.items;
  }

  @ApiOperation({ summary: 'Get subscription increments' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuard)
  @ApiParam({ name: 'account_id', description: 'Account ID' })
  @ApiResponse({ status: 200, description: 'Subscription increments found' })
  @Get(':account_id/increments')
  async getSubscriptionIncrements(@Param('account_id') accountId: string) {
    if (!accountId) {
      return {
        error: true,
        message: 'Account ID is required',
      };
    }
    return this.subscriptionService.getSubscriptionIncrements(accountId);
  }

  @ApiOperation({ summary: 'Get subscription invoices with subscription' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuard)
  @ApiParam({ name: 'id', description: 'Subscription ID' })
  @ApiResponse({ status: 200, description: 'Invoices found' })
  @Get(':id/invoices/:withSubscription')
  async getSubscriptionInvoices(
    @Param('id') id: string,
    @Param('withSubscription') withSubscription: boolean,
    @Query() pagination: PaginationDto,
  ) {
    return this.subscriptionService.getSubscriptionInvoices(id, withSubscription);
  }

  @ApiOperation({ summary: 'Get invoice boleto URL' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuard)
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @ApiResponse({ status: 200, description: 'Boleto URL found' })
  @Get('invoices/:id/boleto')
  async getInvoiceBoleto(@Param('id') id: string) {
    return this.pagarmeService.getInvoice(id);
  }

  @ApiOperation({ summary: 'Cancel subscription' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuardWithAccountAndQISubscription)
  @ApiParam({ name: 'id', description: 'Subscription ID' })
  @ApiResponse({ status: 200, description: 'Subscription cancelled' })
  @Post('cancel/:id')
  async cancelSubscription(@Param('id') id: string, @Request() req: any) {
    const currentSubscription = req.currentSubscription;
    if (!currentSubscription) {
      return {
        error: true,
        message: 'Subscription not found',
      };
    }
    if (currentSubscription.id !== id) {
      return {
        error: true,
        message: 'Subscription is not current',
      };
    }
    if (currentSubscription.status === QISubscriptionStatus.CANCELED) {
      return {
        error: true,
        message: 'Subscription already canceled',
      };
    }
    return this.subscriptionService.scheduleCancelSubscription(id).then(() => {
      const plan = currentSubscription.items.find((item) => item.type === 'plan');
      const {
        nextBillingDate,
        currentPeriodStart,
        currentPeriodEnd,
        ...rest
      } = currentSubscription;
      return {
        id,
        ...rest,
        nextBillingDate: nextBillingDate.toDate().toISOString(),
        currentPeriodStart: currentPeriodStart.toDate().toISOString(),
        currentPeriodEnd: currentPeriodEnd.toDate().toISOString(),
        scheduledAction: QIScheduledAction.CANCEL,
        plan: plan.name,
        amount: plan.totalPrice,
        next_billing_date: nextBillingDate.toDate().toISOString(),
        interval: currentSubscription.billingInterval,
        isCurrent: true,
      };
    });
  }

  @ApiOperation({ summary: 'Activate subscription' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuardWithAccountAndQISubscription)
  @ApiParam({ name: 'id', description: 'Subscription ID' })
  @ApiResponse({ status: 200, description: 'Subscription activated' })
  @Delete('cancel/:id')
  async activateSubscription(@Param('id') id: string, @Request() req: any) {
    const currentSubscription = req.currentSubscription;
    if (!currentSubscription) {
      return {
        error: true,
        message: 'Subscription not found',
      };
    }
    if (currentSubscription.id !== id) {
      return {
        error: true,
        message: 'Subscription is not current',
      };
    }
    if (currentSubscription.scheduledAction !== QIScheduledAction.CANCEL) {
      return {
        error: true,
        message: 'Subscription is not canceled',
      };
    }
    return this.subscriptionService.cancelScheduledAction(id).then(() => {
      const {
        nextBillingDate,
        currentPeriodStart,
        currentPeriodEnd,
        ...rest
      } = currentSubscription;
      const plan = currentSubscription.items.find((item) => item.type === 'plan');
      return {
        id,
        ...rest,
        nextBillingDate: nextBillingDate.toDate().toISOString(),
        currentPeriodStart: currentPeriodStart.toDate().toISOString(),
        currentPeriodEnd: currentPeriodEnd.toDate().toISOString(),
        scheduledAction: null,
        plan: plan.name,
        amount: plan.totalPrice,
        next_billing_date: nextBillingDate.toDate().toISOString(),
        interval: currentSubscription.billingInterval,
        isCurrent: true,
      };
    });
  }

  @ApiOperation({ summary: 'Get my subscription details' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuardWithAccount)
  @ApiResponse({ status: 200, description: 'Subscription details found' })
  @Get('/my')
  getSubscriptionDetails(@Request() req: any) {
    return this.subscriptionService.getSubscriptionDetails(
      req.account,
    );
  }

  @ApiOperation({ summary: 'Daily process subscriptions' })
  @ApiBearerAuth()
  @UseGuards(ServiceAuthGuard)
  @ApiBody({ type: QISubscriptionToProcessDto })
  @ApiResponse({ status: 200, description: 'Subscriptions processed' })
  @Post('/process')
  async processSubscriptions(@Body() subscriptions: QISubscriptionToProcess[]) {
    if (!subscriptions?.length) {
      return {
        error: true,
        message: 'Subscriptions is required',
      };
    }

    // Sortear as activate por último
    subscriptions.sort((a, b) => {
      if (a.action === 'activate') return 1;
      if (b.action === 'activate') return -1;
      return 0;
    });

    return Promise.all(subscriptions.map(async (subscription) => {
      switch (subscription.action) {
        case 'activate':
          Logger(subscription.id, 'Ativando assinatura');
          return this.subscriptionService.updateSubscriptionStatus(
            subscription.id,
            QISubscriptionStatus.ACTIVE,
          ).then((updated) => {
            if (updated) {
              this.accountService.updateAccount(subscription.accountId, {
                active: true,
              });
            }
            return {
              error: !updated,
              message: updated ? 'Assinatura ativada' : 'Erro ao ativar assinatura',
            };
          });
        case 'cancel':
          Logger(subscription.id, 'Cancelando assinatura');
          return this.subscriptionService.updateSubscriptionStatus(
            subscription.id,
            QISubscriptionStatus.CANCELED,
          ).then((updated) => {
            if (updated) {
              this.accountService.updateAccount(subscription.accountId, {
                active: false,
              });
            }
            return {
              error: !updated,
              message: updated ? 'Assinatura cancelada' : 'Erro ao cancelar assinatura',
            };
          });
        case 'expire':
          Logger(subscription.id, 'Expirando assinatura');
          return this.subscriptionService.updateSubscriptionStatus(
            subscription.id,
            QISubscriptionStatus.EXPIRED,
          ).then((updated) => {
            // TODO: desativar conta
            return {
              error: !updated,
              message: updated ? 'Assinatura expirada' : 'Erro ao expirar assinatura',
            };
          });
        case 'renewal':
          Logger(subscription.id, 'Processando renovação para assinatura');
          return this.paymentService.renewal(subscription);
        case 'billing':
          Logger(subscription.id, 'Processando cobrança (antecipada para pix e boleto) para assinatura');
          return this.paymentService.charge(subscription);
        case 'block-access':
          Logger(subscription.id, 'Desativando conta');
          // Assinatura com invoices
          const subscriptionData = subscription as unknown as QISubscription & { invoices: QISubscriptionInvoice[] };
          const paymentStatus = await this.paymentService.getCharges(subscriptionData);
          if (paymentStatus.error) {
            Logger(subscription.id, 'Erro ao checar status de pagamento');
            return paymentStatus;
          }

          const invoicesPending = subscriptionData.invoices;
          // Verificar se alguma invoice está paga
          // Caso esteja, atualizamos o status no banco e pulamos para o próximo
          for (const invoice of paymentStatus.data) {
            if (invoice.status === 'paid') {
              Logger(invoice.id, 'Invoice paid');
              const invoicePending = invoicesPending.find((item) => item.chargeId === invoice.id)!;
              await this.subscriptionService.updateSubscriptionInvoice(
                subscription.id,
                invoicePending.id,
                { status: 'paid' },
              );
              invoicesPending.splice(invoicesPending.indexOf(invoice), 1);
              continue;
            } else {
              const isDue = invoice.due_at.toMillis() <= Date.now();
              if (!isDue) {
                Logger(invoice.id, 'Invoice not due');
                invoicesPending.splice(invoicesPending.indexOf(invoice), 1);
                continue;
              };
            }
          }

          // Se todas as invoices estão pagas, não desativar conta
          if (invoicesPending.length === 0) {
            return {
              error: false,
              message: 'Todas as cobranças foram pagas',
            };
          }

          // Atualizar status da assinatura para pendente
          await this.subscriptionService.updateSubscription(subscription.id, { status: QISubscriptionStatus.PENDING })

          return this.accountService.updateAccount(subscription.accountId, {
            active: false,
          }).then(() => {
            return {
              error: false,
              message: 'Conta desativada',
            };
          });
        default:
          Logger(subscription.id, 'Ação não suportada');
          return {
            error: true,
            message: 'Ação não suportada',
          };
      }
    }));
  }

  @ApiOperation({ summary: 'List renewal' })
  @ApiBearerAuth()
  @ApiBody({ type: QISubscriptionToProcessDtoList })
  @ApiResponse({ status: 200, description: 'Renewal listed' })
  @Post('/list')
  async listRenewal() {
    return (await this.subscriptionService.listSubscriptions())
      .flatMap((subscription) => {
        return {
          ...subscription,
          startDate: subscription.startDate.toDate().getTime(),
          endDate: subscription.endDate?.toDate().getTime(),
          nextBillingDate: subscription.nextBillingDate.toDate().getTime(),
          currentPeriodStart: subscription.currentPeriodStart.toDate().getTime(),
          currentPeriodEnd: subscription.currentPeriodEnd.toDate().getTime(),
          action: subscription.scheduledAction || 'billing',
        };
      });
  }


  // Buscar subscriptions com datas de inicio em 1970
  // e recalcular as demais datas a partir do createdAt
  @Post('/fix-dates')
  async fixDates() {
    return this.subscriptionService.fixDates();
  }

}

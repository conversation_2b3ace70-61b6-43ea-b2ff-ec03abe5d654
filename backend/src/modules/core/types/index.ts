/**
 * Core Types Module
 * 
 * This module contains shared types and interfaces used across multiple modules.
 * Centralizing these types helps prevent circular dependencies.
 */

// Re-export types from other modules to centralize them
export * from '../../subscription/types/qiplus.types';
export * from '../../pagarme/types/pagarme.type';
export * from '../../payment/types/gateway.types';
export * from '../../payment/entities/paymentStatus.entity';

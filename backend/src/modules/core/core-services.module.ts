import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { AccountsModule } from '../account/account.module';
import { AccountService } from '../account/account.service';
import { AffiliateModule } from '../affiliates/affiliate.module';
import { AffiliateService } from '../affiliates/affiliate.service';
import { FirebaseService } from '../firebase/firebase.service';
import { PlanModule } from '../plan/plan.module';
import { PlanRepository } from '../plan/plan.repository';
import { PlanService } from '../plan/plan.service';
import { PaymentService } from '../payment/payment.service';
import { UpgradeService } from '../upgrade/upgrade.service';
import { UpgradeCalculator, UpgradeCalculatorFromSubscription } from '../payment/utils/upgrade-calculator';
import { SubscriptionService } from '../subscription/subscription.service';
import { SubscriptionRepository } from '../subscription/subscription.repository';
import { PagarmeService } from '../pagarme/pagarme.service';

/**
 * CoreServicesModule contains services that are shared between multiple modules.
 * This helps prevent circular dependencies by centralizing common services.
 */
@Global()
@Module({
  imports: [
    ConfigModule,
    PlanModule,
    AccountsModule,
    AffiliateModule
  ],
  providers: [
    FirebaseService,
    PaymentService,
    UpgradeService,
    UpgradeCalculator,
    UpgradeCalculatorFromSubscription,
    SubscriptionService,
    SubscriptionRepository,
    PagarmeService,
    AccountService,
    PlanService,
    PlanRepository,
    AffiliateService
  ],
  exports: [
    FirebaseService,
    PaymentService,
    UpgradeService,
    UpgradeCalculator,
    UpgradeCalculatorFromSubscription,
    SubscriptionService,
    SubscriptionRepository,
    PagarmeService,
    AccountService,
    PlanService,
    PlanRepository,
    AffiliateService
  ],
})
export class CoreServicesModule { }

import { Injectable } from '@nestjs/common';
import { isObject } from 'class-validator';
import * as admin from 'firebase-admin';
import { Plan } from './model/plan.model';
import { convertPlanAmountToCents } from 'src/utils/plan.utils';

@Injectable()
export class PlanRepository {
  private firestore = admin.firestore();
  private collection = this.firestore.collection('qiplus-plans');

  // Buscar o plano pelo ID
  async findById(id: string): Promise<Plan | null> {
    const doc = await this.collection.doc(id).get();
    const data = doc.data();
    if (isObject(data)) {
      return Plan.parse(convertPlanAmountToCents(data));
    }
    return null;
  }

  async findAll(): Promise<Plan[] | null> {
    const snapshot = await this.collection.where('status', '==', 'publish').get();
    return snapshot.docs.map((doc) => Plan.parse(convertPlanAmountToCents(doc.data())));
  }
}

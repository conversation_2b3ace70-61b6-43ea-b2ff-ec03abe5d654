import { Injectable } from '@nestjs/common';
import { FirebaseError } from 'firebase-admin/lib/utils/error';
import { DefaultResponse } from 'src/modules/firebase/types/response.type';
import { ResponseUtil } from 'src/utils/response';
import { Plan } from './model/plan.model';
import { PlanRepository } from './plan.repository';

@Injectable()
export class PlanService {
  constructor(private readonly planRepository: PlanRepository) {}

  async getPlanById(planId: string): Promise<DefaultResponse<Plan | null>> {
    return this.planRepository
      .findById(planId)
      .then((data) => ResponseUtil.success<Plan>('Plan found', data as Plan))
      .catch((error: FirebaseError) => ResponseUtil.error(error.message));
  }

  async getPlans(): Promise<DefaultResponse<Plan[] | null>> {
    return this.planRepository
      .findAll()
      .then((data) =>
        ResponseUtil.success<Plan[]>('Plans found', data as Plan[]),
      )
      .catch((error: FirebaseError) => ResponseUtil.error(error.message));
  }
}

import { Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { FirebaseError } from 'firebase-admin/lib/utils/error';
import { PaymentStatus } from 'src/modules/payment/entities/paymentStatus.entity';
import { ResponseUtil } from 'src/utils/response';
import serviceAccount from '../../../firebase_service.json';
import { DefaultResponse } from './types/response.type';

@Injectable()
export class FirebaseService {
  private adminAuth: admin.auth.Auth;
  private firestore: FirebaseFirestore.Firestore;

  constructor() {
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(
          serviceAccount as admin.ServiceAccount,
        ),
      });
    }
    this.firestore = admin.firestore();

    this.adminAuth = admin.auth();
  }

  async generateCustomToken(uid: string): Promise<string> {
    return this.adminAuth.createCustomToken(uid);
  }

  async verifyIdToken(idToken: string): Promise<admin.auth.DecodedIdToken> {
    return await this.adminAuth.verifyIdToken(idToken);
  }

  getFirestore(): FirebaseFirestore.Firestore {
    return this.firestore;
  }

  async createUser(
    email: string,
    password: string,
  ): Promise<DefaultResponse<admin.auth.UserRecord | null>> {
    return this.adminAuth
      .createUser({
        email,
        password,
      })
      .then((userRecord) => ResponseUtil.success('User created', userRecord))
      .catch((error: FirebaseError) => ResponseUtil.error(error.message));
  }

  async getAccountByOwner(uid: string): Promise<FirebaseFirestore.DocumentData> {
    const accountRef = this.firestore
      .collection('accounts')
      .where('owner', '==', uid)
      .limit(1);
    const accountSnapshot = await accountRef.get();

    if (accountSnapshot.empty) {
      return {
        error: true,
        message: 'Conta não encontrada',
        data: null,
      };
    }

    const account = accountSnapshot.docs[0].data();

    delete account.keywords;

    return { data: account, docRef: accountRef };
  }

  async getAccountByUID(uid: string): Promise<FirebaseFirestore.DocumentData> {
    const accountRef = this.firestore
      .collection('accounts')
      .where('uid', '==', uid)
      .limit(1);
    const accountSnapshot = await accountRef.get();

    if (accountSnapshot.empty) {
      return {
        error: true,
        message: 'Conta não encontrada',
        data: null,
      };
    }

    const account = accountSnapshot.docs[0].data();

    delete account.keywords;

    return { data: account, docRef: accountRef };
  }

  async getAccountByID(id: string): Promise<FirebaseFirestore.DocumentData> {
    try {
      const accountRef = this.firestore.collection('accounts').doc(id);
      const docSnapshot = await accountRef.get();

      if (!docSnapshot.exists) {
        console.error(`Documento com ID ${id} não encontrado.`);
        return {
          error: true,
          message: 'Documento não encontrado',
          data: null,
        };
      }

      const data = {
        error: false,
        message: 'Documento encontrado',
        data: docSnapshot.data(),
      };
      return data;
    } catch (error) {
      const data = {
        error: true,
        message: 'Erro ao buscar conta',
        data: error,
      };
      return data;
    }
  }

  async getUserByUID(uid: string): Promise<FirebaseFirestore.DocumentData> {
    const userRef = this.firestore
      .collection('qiusers')
      .where('uid', '==', uid)
      .limit(1);
    const userSnapshot = await userRef.get();

    if (userSnapshot.empty) {
      const data = {
        error: true,
        message: 'Usuário não encontrado',
        data: null,
      };
      return data;
    } else {
      const data = {
        error: false,
        message: 'Usuário encontrado',
        data: userSnapshot.docs[0].data(),
      };
      return data;
    }
  }

  async updateAccountPayment(
    accountId: string,
    paymentData: any,
    payment_status: PaymentStatus,
    active: boolean,
  ): Promise<void> {
    const accountRef = this.firestore.collection('accounts').doc(accountId);
    accountRef.update({ payment_status, active });

    //TODO: adjust payment
    // {
    //   sub_due_at: "25-03-2025",
    //   gateway: "pagarme"
    // }
    accountRef.collection('logs_payments').doc(paymentData.id).set(paymentData);
  }

  async updatePayment(
    accountId: string,
    paymentData: any,
    payment_status: PaymentStatus,
    isUpgrade: boolean,
  ) {
    const accountRef = this.firestore.collection('accounts').doc(accountId);
    const updateFields: any = {};
    if (isUpgrade) {
      updateFields['payment_upgrade_status'] = payment_status;
    } else {
      updateFields['payment_status'] = payment_status;
    }
    updateFields['planId'] = payment_status.planId;
    if (payment_status.status === 'paid') {
      updateFields['active'] = true;
    }
    accountRef.update(updateFields);

    accountRef.collection('logs_payments').doc(paymentData.id).set(paymentData);
  }

  async deleteUser(uid: string): Promise<void> {
    await this.adminAuth.deleteUser(uid);
  }
}

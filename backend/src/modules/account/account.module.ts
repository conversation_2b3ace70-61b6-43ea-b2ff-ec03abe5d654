import { Module } from '@nestjs/common';
import { PlanModule } from '../plan/plan.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { AccountRepository } from './account.repository';
import { AccountService } from './account.service';

@Module({
  imports: [SubscriptionModule, PlanModule],
  providers: [AccountRepository, AccountService],
  exports: [AccountRepository, AccountService],
})
export class AccountsModule {}

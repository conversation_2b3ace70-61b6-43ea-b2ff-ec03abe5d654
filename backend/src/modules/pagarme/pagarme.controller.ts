import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { CustomerDto } from './dto/customer.dto';
import { PagarmeService } from './pagarme.service';
import { CreateCard, SubscriptionRequest } from './types/pagarme.type';

@Controller('/pagarme')
export class PagarmeController {
  constructor(private readonly pagarmeService: PagarmeService) {}
  @Get('/customers')
  getAllCustomers() {
    return this.pagarmeService.listCostumers();
  }

  @Get('/customer/:id')
  getCustomer(@Param('id') id: string) {
    return this.pagarmeService.getCustomer(id);
  }

  @Post('/customers')
  async createCustomer(@Body() customerData: CustomerDto) {
    return this.pagarmeService.createCustomer(customerData);
  }

  @Get('/cards/:customer_id')
  getCards(@Param('customer_id') customer_id: string) {
    return this.pagarmeService.listCards(customer_id);
  }

  @Post('/cards/create')
  async createCard(@Body() cardData: CreateCard) {
    return this.pagarmeService.createCard(cardData);
  }

  @Post('/subscriptions')
  async createSubscription(@Body() subscriptionData: SubscriptionRequest) {
    return this.pagarmeService.createSubscription(subscriptionData);
  }
}

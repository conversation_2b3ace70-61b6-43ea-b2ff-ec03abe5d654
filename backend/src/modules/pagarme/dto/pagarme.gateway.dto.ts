import { GatewayTypes } from 'src/modules/payment/entities/gateway.interface';
import { SubscriptionRequest } from '../types/pagarme.type';
import { CustomerDto } from './customer.dto';
import { PaginationDto } from './pagination.dto';

export class PagarmeGatewayType implements GatewayTypes {
  listCostumersIn: PaginationDto;
  listCostumersOut: any;
  createCustomerIn: any;
  createCustomerOut: any;
  updateCustomerIn: any;
  updateCustomerOut: any;
  getCustomerIn: any;
  getCustomerOut: any;
  findCustomerDocumentIn: any;
  findCustomerDocumentOut: any;
  findCustomerByUidIn: any;
  findCustomerByUidOut: any;
  findCustomerIdByUidIn: any;
  findCustomerIdByUidOut: any;
  listCardsIn: PaginationDto;
  listCardsOut: any;
  createCardIn: any;
  createCardOut: any;
  deleteCardIn: any;
  deleteCardOut: any;
  getCardIn: any;
  getCardOut: any;
  listSubscriptionsIn: PaginationDto;
  listSubscriptionsOut: any;
  createSubscriptionIn: any;
  createSubscriptionOut: any;
  getSubscriptionIn: any;
  getSubscriptionOut: any;
  updateSubscriptionIn: any;
  updateSubscriptionOut: any;
  cancelSubscriptionIn: any;
  cancelSubscriptionOut: any;
  updateSubscriptionMetadataIn: any;
  updateSubscriptionMetadataOut: any;
  enableManualBillingIn: any;
  enableManualBillingOut: any;
  disableManualBillingIn: any;
  disableManualBillingOut: any;
  listInvoicesIn: PaginationDto;
  listInvoicesOut: any;
  getInvoiceIn: any;
  getInvoiceOut: any;
  getSubscriptionItemsIn: any;
  getSubscriptionItemsOut: any;
  getSubscriptionIncrementsIn: any;
  getSubscriptionIncrementsOut: any;
  createOrderIn: any;
  createOrderOut: any;
  cancelOrderIn: any;
  cancelOrderOut: any;
  listRecipientsIn: PaginationDto;
  listRecipientsOut: any;
  editSplitIn: any;
  editSplitOut: any;
  createCustomer: CustomerDto;
  createSubscription: SubscriptionRequest;
  getChargeIn: any;
  getChargeOut: any;
}

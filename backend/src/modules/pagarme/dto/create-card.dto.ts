import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsString, Matches, ValidateNested } from 'class-validator';
import { AddressDto } from 'src/modules/auth/dto/address.dto';

export class CreateCardDto {
  @ApiProperty({ description: 'Número do cartão' })
  @IsNotEmpty()
  @IsString()
  number: string;

  @ApiProperty({ description: 'Nome do titular do cartão' })
  @IsNotEmpty()
  @IsString()
  holder_name: string;

  @ApiProperty({ description: 'Mês e ano de expiração do cartão (MM/AA)' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{2}\/\d{2}$/, {
    message: 'A data de expiração deve ser no formato MM/AA',
  })
  cardExpiry?: string;

  @ApiProperty({ description: 'Código de segurança do cartão (CVV)' })
  @IsNotEmpty()
  @IsString()
  cvv: string;

  @ApiProperty({ description: 'Endereço de cobrança' })
  @IsNotEmpty({ message: 'Endereço de cobrança é obrigatório' })
  @ValidateNested()
  @Type(() => AddressDto)
  billing_address: AddressDto;

  // Este campo será preenchido automaticamente pelo controller
  customer_id?: string;
}

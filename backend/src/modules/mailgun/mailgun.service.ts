import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import FormData from 'form-data';
import * as fs from 'fs';
import Mailgun from 'mailgun.js';


@Injectable()
export class MailgunService {
    constructor(){}
    private mailgun = new Mailgun(FormData).client({
        username: 'api',
        key: process.env.MAILGUN_API_KEY || 'key-here'
    })

    private mailgunInstance = axios.create({
        baseURL: 'https://api.mailgun.net/v3/mail.qi.plus/',
        headers: {
            Authorization: 'Basic ' + Buffer.from(`api:${process.env.MAILGUN_API_KEY}`).toString('base64'),
        },
    });

    async sendEmail(to: string, subject: string, html: string, attachment?: string) {
        const deliveryInDev =
            process.env.MAILGUN_DELIVERY_IN_DEV !== 'false';
        const IS_DEV = process.env.NODE_ENV !== 'production';
        if (!deliveryInDev && IS_DEV) {
            Logger.log('MAILGUN_DELIVERY_IN_DEV is false');
            return;
        }
        let data = new FormData();
        data.append('from', 'QIPLUS <<EMAIL>>');
        data.append('to', to);
        data.append('subject', subject);
        data.append('html', html)
        attachment && data.append('attachment', fs.createReadStream(attachment))
        this.mailgunInstance.post('/messages', data)
        .then(res => {
                console.log(res.data)
            })
            .catch(err => {
                console.log(err)
            })
    }
}

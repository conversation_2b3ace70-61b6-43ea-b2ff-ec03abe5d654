import { OmitType } from '@nestjs/mapped-types';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { FeatureDto } from 'src/modules/auth/dto/feature.dto';

// DTO que representa cada item de customFeatures
export class UpgradePreviewDto {
  @IsNumber({}, { message: 'Quantidade de leads deve ser um número' })
  @IsNotEmpty({ message: 'Quantidade de leads é obrigatória' })
  leadsCount: number;

  @IsArray()
  @IsString({ each: true })
  additionals: string[];

  @IsNumber()
  @IsNotEmpty()
  installments: number;

  // OTHER DATA
  @IsNumber()
  @IsNotEmpty()
  discount: number;

  @IsString()
  planId: string;

  @IsBoolean()
  isYearly: boolean;

  // NOVA PROPRIEDADE: customFeatures
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FeatureDto)
  customFeatures: FeatureDto[];

  @IsNumber()
  @IsNotEmpty()
  billingDay: number;
}

export class PlanPreviewDto extends OmitType(UpgradePreviewDto, [
  'discount',
  'installments',
  'planId',
]) { }

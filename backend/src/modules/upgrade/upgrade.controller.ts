import {
  Body,
  Controller,
  HttpCode,
  Post,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { OptionalFirebaseAuthGuardWithAccountAndSubscription } from 'src/modules/auth/optional-firebase-auth.guard';
import { UpgradeService } from 'src/modules/upgrade/upgrade.service';
import { PlanPreviewDto } from './dto/upgrade-preview.dto';

@Controller('upgrade')
export class UpgradeController {
  constructor(private upgradeService: UpgradeService) {}

  @Post('/plans')
  @HttpCode(200)
  @ApiBearerAuth()
  @UsePipes(new ValidationPipe({ transform: true }))
  @UseGuards(OptionalFirebaseAuthGuardWithAccountAndSubscription)
  @ApiBody({ type: PlanPreviewDto })
  @ApiOperation({
    summary:
      'Get plans - returns calculated plans for authenticated users or basic plans for public users',
  })
  @ApiResponse({
    status: 200,
    description:
      'List of plans - with calculations if authenticated, without if not',
  })
  async getPlans(@Body() body: PlanPreviewDto, @Req() request: any) {
    // Check if the request is authenticated
    if (
      request.isAuthenticated &&
      request.account &&
      request.currentSubscription
    ) {
      // Authenticated user with subscription - return calculated plans
      return this.upgradeService.plansPreview(
        request.currentSubscription,
        request.account,
        body,
      );
    } else {
      // Public user or authenticated without subscription - return basic plans
      return this.upgradeService.getPlans();
    }
  }
}

export type PagarmeWebhook = {
  id: string;
  account: any;
  type: string;
  created_at: Date;
  data: {
    subscription: any;
    id: string;
    code: string;
    start_at: Date;
    interval: string;
    interval_count: number;
    billing_type: string;
    current_cycle: {
      id: string;
      start_at: Date;
      end_at: Date;
      billing_at: string;
      status: string;
      cycle: number;
    };
    next_billing_at: string;
    payment_method: string;
    currency: string;
    installments: number;
    status: string;
    created_at: Date;
    updated_at: Date;
    customer: {
      id: string;
      name: string;
      email: string;
      code: string;
      document: string;
      document_type: string;
      type: string;
      gender: string;
      delinquent: boolean;
      created_at: Date;
      updated_at: Date;
      birthdate: string;
      phones: {
        home_phone: {
          country_code: number;
          number: number;
          area_code: number;
        };
      };
    };
    card: {
      id: string;
      first_six_digits: number;
      last_four_digits: number;
      brand: string;
      holder_name: string;
      exp_month: number;
      exp_year: number;
      status: string;
      type: string;
      created_at: Date;
      updated_at: Date;
    };
    plan: {
      id: string;
      name: string;
      description: string;
      url: string;
      interval: string;
      interval_count: number;
      billing_type: string;
      payment_methods: string[];
      installments: number[];
      status: string;
      currency: string;
      created_at: Date;
      updated_at: Date;
    };
    items: Items[];
    boleto: {};
    metadata: any;
  };
};

export interface WebhookInvoice {
  id: string;
  account: any;
  type: string;
  created_at: Date;
  data: Data;
}

export interface Data {
  id: string;
  code: string;
  url: string;
  amount: number;
  total_discount: number;
  total_increment: number;
  status: string;
  payment_method: string;
  due_at: string;
  created_at: string;
  items: Item[];
  customer: {
    id: string;
    name: string;
    email: string;
    code: string;
    document: string;
    document_type: string;
    type: string;
    deliquent: boolean;
    created_at: Date;
    updated_at: Date;
    birthdate: Date;
    phones: {
      home_phone: {
        country_code: number;
        number: number;
        area_code: number;
      };
    };
  };
  subscription: Subscription;
  cycle: Cycle;
  billing_address: any;
  charge: Charge;
  metadata: any;
}

interface Item {
  subscription_item_id: string;
  name: string;
  description: string;
  amount: number;
  quantity: number;
  pricing_scheme: PricingScheme;
}

interface PricingScheme {
  price: number;
  type: string; // Assuming 'type' is a required field based on the provided data
}

interface Subscription {
  id: string;
  code: string;
  start_at: Date;
  interval: string;
  interval_count: number;
  billing_type: string;
  next_billing_at: Date;
  payment_method: string;
  currency: string;
  installments: number;
  minimum_price: number;
  status: string;
  created_at: Date;
  updated_at: Date;
  metadata: any;
}

interface Cycle {
  id: string;
  start_date: string;
  end_date: string;
  billing_at: string;
  status: string;
  cycle: number;
}

interface Charge {
  id: string;
  code: string;
  amount: number;
  paid_amount: number;
  status: string;
  currency: string;
  payment_method: string;
  due_at: Date;
  paid_at: string;
  created_at: string;
  updated_at: string;
  last_transaction: LastTransaction;
  metadata: any;
}

interface LastTransaction {
  id: string;
  transaction_type: string;
  gateway_id: string;
  amount: number;
  status: string;
  success: boolean;
  paid_amount: number;
  paid_at: string;
  url: string;
  pdf: string;
  line: string;
  barcode: string;
  qr_code: string;
  nosso_numero: string;
  bank: string;
  instructions: string;
  due_at: string;
  created_at: string;
  updated_at: string;
  gateway_response: any; // Assuming 'gateway_response' is an object based on the provided data
  antifraud_response: any; // Assuming 'antifraud_response' is an object based on the provided data
  metadata: any;
}

export interface Items {
  id: string;
  name: string;
  description: string;
  quantity: number;
  status: string;
  created_at: Date;
  updated_at: Date;
  pricing_scheme: {
    price: number;
    scheme_type: string;
  };
}

import { Injectable, Logger } from '@nestjs/common';
import { PagarmeService } from 'src/modules/pagarme/pagarme.service';
import { PaymentStatus } from 'src/modules/payment/entities/paymentStatus.entity';
import { FirebaseService } from '../firebase/firebase.service';
import { MailgunService } from '../mailgun/mailgun.service';
import { WebhookInvoice } from './types/webhook.types';
@Injectable()
export class WebhookService {
  constructor(
    private readonly firebaseService: FirebaseService,
    private pagarmeService: PagarmeService,
  ) { }
  private pagarmeWebhook: WebhookInvoice;

  create(pagarmeWebhook: WebhookInvoice) {
    this.pagarmeWebhook = pagarmeWebhook;
  }

  get(): WebhookInvoice {
    return this.pagarmeWebhook;
  }

  async getAccount() {
    const accountId = this.getMetaData()?.accountId || null;
    if (accountId) {
      return this.firebaseService.getAccountByID(accountId).then((account) => {
        return account.data;
      });
    }
    const uid = this.getMetaData()?.uid || null;
    if (uid) {
      return this.firebaseService.getAccountByOwner(uid).then((account) => {
        return account.data;
      });
    }
    return null;
  }

  getMetaData() {
    return this.pagarmeWebhook.data.metadata;
  }

  getData(): WebhookInvoice['data'] {
    return this.pagarmeWebhook.data;
  }

  getInvoices() {
    return this.pagarmeService.listInvoices(this.getData().id);
  }

  getSubscriptions() {
    return this.pagarmeService.listSubscriptions(this.getData().customer.id);
  }

  cancelSubscription(subscription_id: string) {
    return this.pagarmeService.cancelSubscription(subscription_id);
  }

  updateAccountPayment(
    accountId: string,
    paymentData: any,
    payment_status: PaymentStatus,
    active: boolean,
  ) {
    return this.firebaseService.updateAccountPayment(
      accountId,
      paymentData,
      payment_status,
      active,
    );
  }

  updatePayment(
    accountId: string,
    paymentData: any,
    payment_status: PaymentStatus,
    isUpgrade: boolean,
  ) {
    return this.firebaseService.updatePayment(
      accountId,
      paymentData,
      payment_status,
      isUpgrade,
    );
  }

  async getUserByUID() {
    return this.firebaseService.getUserByUID(this.getMetaData().uid);
  }

  sendMail(to: string, subject: string, html: string, attachment?: string) {
    const mailgunService = new MailgunService();

    const mail = mailgunService.sendEmail(
      to,
      subject,
      html,
      attachment && attachment,
    );

    Logger.log(mail, 'MAIL SENT');
  }

  generateCustomToken(uid: string) {
    return this.firebaseService.generateCustomToken(uid);
  }

  enableManualBilling(subscriptionId: string) {
    return this.pagarmeService.enableManualBilling(subscriptionId);
  }

  disableManualBilling(subscriptionId: string) {
    return this.pagarmeService.disableManualBilling(subscriptionId);
  }
}

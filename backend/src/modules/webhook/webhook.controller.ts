import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBody } from '@nestjs/swagger';
import { AccountService } from 'src/modules/account/account.service';
import { PagarmeEvent } from 'src/modules/pagarme/types/pagarme.event.enum';
import { PlanService } from 'src/modules/plan/plan.service';
import { Gateway } from 'src/types/gateway.enum';
import Logger from 'src/utils/logger';
import { calculateYearlyBillingDate } from 'src/utils/subscription.helper';
import { SubscriptionService } from '../subscription/subscription.service';
import {
  QIBillingInterval,
  QIPaymentMethod,
  QISubscriptionStatus,
} from '../subscription/types/qiplus.types';
import {
  QiAccountInterceptorRequest,
  QiAccountRequest,
} from './interceptor/qiaccount.interceptor';
import PaymentBoleto from './layout/payment.boleto';
import PaymentSuccess from './layout/payment.paid';
import PaymentUpgrade from './layout/payment.upgrade';
import { PaymentFailed } from './types/payment_failed.type';
import { WebhookService } from './webhook.service';
import { PaymentMethod } from '../payment/enum/paymentMethod.enum';
import { addDays, addYears, formatForEmail, toFirebaseTimestamp } from 'src/utils/date.utils';
import { calculateInstallmentDueDate } from '../payment/utils/payment.utils';

@Controller('webhook')
export class WebhookController {
  constructor(
    private webhookService: WebhookService,
    private planService: PlanService,
    private accountService: AccountService,
    private subscriptionService: SubscriptionService,
  ) { }

  @ApiBody({})
  @Post('/subscription')
  @UseGuards(QiAccountInterceptorRequest)
  async handleWebhook(@Body() body: any, @Req() request: QiAccountRequest) {
    if (!body) return { message: 'No body' };
    if (!request.account) return { message: 'Account not found' };

    Logger(body, 'WEBHOOK RECEIVED');
    switch (body.type) {
      case PagarmeEvent.INVOICE_PAID:
      case PagarmeEvent.INVOICE_PAYMENT_FAILED:
      case PagarmeEvent.INVOICE_PAYMENT_CANCELED:
      case PagarmeEvent.INVOICE_CREATED:
        this.handleInvoice(body);
        break;
      case PagarmeEvent.CHARGE_PAID:
      case PagarmeEvent.CHARGE_PAYMENT_FAILED:
      case PagarmeEvent.CHARGE_PENDING:
        this.handleCharge(body, request);
        break;
      case PagarmeEvent.ORDER_PAYMENT_FAILED:
        this.orderPaymentFailed(body, request);
        break;
      default:
        break;
    }

    return { message: 'Webhook received' };
  }

  async handleInvoice(body: any) {
    const active =
      body.type == PagarmeEvent.INVOICE_PAID ||
      body.type == PagarmeEvent.INVOICE_CREATED;
    this.webhookService.create(body);

    const webhookData = this.webhookService.getData();

    const account = await this.webhookService.getAccount();

    if (!account) return { message: 'Account not found' };

    const accountId = account.id;
    const { charge, subscription, metadata } = webhookData;

    if (active) {
      if (body.type === PagarmeEvent.INVOICE_PAID) {
        const subscriptions = await this.webhookService.getSubscriptions();
        for (const subscription of subscriptions.data) {
          if (
            subscription.status == 'active' &&
            subscription.id != this.webhookService.getData().subscription.id
          ) {
            this.webhookService.cancelSubscription(subscription.id);
          }
        }
      }

      if (
        body.type == PagarmeEvent.INVOICE_CREATED &&
        charge.payment_method == 'boleto'
      ) {
        const user = await this.webhookService.getUserByUID();
        const userData = user.data;
        this.webhookService.sendMail(
          userData.email,
          'QI PLUS - Pagamento de Boleto para Ativação da Assinatura',
          PaymentBoleto(
            charge.amount,
            formatForEmail(charge.due_at.getTime()),
            charge.last_transaction.line,
            charge.last_transaction.url,
          ),
        );
      } else if (body.type == 'invoice.paid') {
        // Tenta obter o nome do plano a partir do título da conta, metadata ou planId da conta
        let planName = account?.title?.split('-')[1];

        if (!planName) {
          const planId = metadata.planId || account?.planId;
          if (planId) {
            const plan = await this.planService.getPlanById(planId);
            planName = plan.data?.name;
          }
        }
        const user = await this.webhookService.getUserByUID();
        const { uid, email } = user.data;

        const userToken = await this.webhookService.generateCustomToken(uid);

        this.webhookService.sendMail(
          email,
          'QI PLUS - Parabéns! Assinatura ativada com sucesso!',
          PaymentSuccess(
            formatForEmail(webhookData.subscription.start_at.getTime()),
            formatForEmail(webhookData.subscription.next_billing_at.getTime()),
            planName,
            `${process.env.APP_URL}/app/login?token=${userToken}`,
          ),
          'folder_QIPlus_WEB_ok.pdf',
        );
      }
    }

    let error_message = '';
    if (body.type == PagarmeEvent.INVOICE_PAYMENT_FAILED) {
      const data = body as PaymentFailed;
      error_message =
        data.data?.charge?.last_transaction?.acquirer_message ||
        account.payment_status?.error_message ||
        '';
    }

    if (body) {
      this.webhookService.updateAccountPayment(
        accountId,
        body,
        {
          status: body.data.status,
          error_message,
          gateway: Gateway.PAGARME,
          subscription_id: subscription.id,
          planId: metadata.plan_id || account?.planId,
        },
        active,
      );
    }

    //Logger({ body, payment_status: body.data.status });
  }

  async orderPaymentFailed(body: any, request: QiAccountRequest) {
    const { charges } = body.data;
    const charge = charges[0];

    if (!charge) return;

    body.charge = charge;
    body.last_transaction = charge.last_transaction;
    this.handleCharge(body, request);
  }

  async handleCharge(
    body: any,
    request: QiAccountRequest,
    isRecurring = false,
  ) {

    const { id: chargeId, last_transaction, metadata } = body.data;

    // Valor do desconto de upgrade
    const upgradeCredit = metadata?.upgradeCredit || 0;

    // Parcela atual
    const installment = Number(metadata?.installmentNumber || 1);

    const IS_DEV = process.env.NODE_ENV !== 'production';
    if (body.data.status == 'paid' && !isRecurring && IS_DEV && body.data.payment_method == PaymentMethod.PIX) {
      console.log('CHARGE PAID, WAITING 30 SECONDS TO PROCESS...');
      const delay = () =>
        new Promise((resolve) => setTimeout(resolve, 10 * 1000 * installment));
      await delay();
      this.handleCharge(body, request, true);
      return;
    }
    const { account, currentSubscription, subscriptionToProcess } = request;
    const acquirer_message =
      body.data.last_transaction?.acquirer_message ||
      (body.data.payment_method == PaymentMethod.PIX
        ? 'Pagamento não realizado.'
        : 'Falha ao processar pagamento.');
    const isUpgrade =
      !!subscriptionToProcess && (subscriptionToProcess.isUpgrade || false);
    Logger(subscriptionToProcess?.id, 'IS UPGRADE: ' + isUpgrade);

    if (!account) {
      console.log('Account not found');
      return;
    }

    if (!subscriptionToProcess && !currentSubscription) {
      console.log('No subscription to process or current subscription');
      return;
    }

    // Atualiza o status das faturas da assinatura no banco de dados
    if (last_transaction && subscriptionToProcess) {
      const transactionId = `${last_transaction.transaction_type}_${chargeId}_${installment}`;
      switch (subscriptionToProcess?.paymentMethod) {
        case QIPaymentMethod.PIX:
          await this.subscriptionService.setSubscriptionInvoice(
            subscriptionToProcess.id,
            transactionId,
            {
              id: transactionId,
              chargeId,
              amount: last_transaction.amount,
              due_at: toFirebaseTimestamp(last_transaction.due_at || last_transaction.expires_at),
              status: body.data.status,
              transaction_type: last_transaction.transaction_type,
              qr_code: last_transaction.qr_code || '',
              url: last_transaction.qr_code_url || '',
              identifier: last_transaction.id,
              created_at: toFirebaseTimestamp(
                last_transaction.created_at,
              ),
              installment,
              upgradeCredit,
            },
          );
          break;
        case QIPaymentMethod.BOLETO:
          await this.subscriptionService.setSubscriptionInvoice(
            subscriptionToProcess.id,
            transactionId,
            {
              id: transactionId,
              chargeId,
              amount: last_transaction.amount,
              due_at: toFirebaseTimestamp(last_transaction.due_at),
              status: body.data.status,
              transaction_type: last_transaction.transaction_type,
              url: last_transaction.url,
              barcode: last_transaction.barcode,
              qr_code: last_transaction.qr_code,
              pdf: last_transaction.pdf,
              line: last_transaction.line,
              created_at: toFirebaseTimestamp(
                last_transaction.created_at,
              ),
              installment,
              upgradeCredit,
            },
          );
          break;
        case QIPaymentMethod.CREDIT_CARD:
          await this.subscriptionService.setSubscriptionInvoice(
            subscriptionToProcess.id,
            transactionId,
            {
              id: transactionId,
              chargeId,
              amount: last_transaction.amount,
              status: body.data.status,
              transaction_type: last_transaction.transaction_type,
              identifier: last_transaction.id,
              created_at: toFirebaseTimestamp(
                last_transaction.created_at,
              ),
              installment,
              upgradeCredit,
            },
          );
          break;
      }
    }

    // Atualiza o status do pagamento na conta
    switch (body.data.status) {
      case 'failed':
        // Atualiza o status do pagamento na conta
        this.webhookService.updatePayment(
          account.id!,
          body,
          {
            status: 'failed',
            error_message: acquirer_message,
            gateway: Gateway.PAGARME,
            subscription_id: subscriptionToProcess!.id,
            planId: subscriptionToProcess!.planId,
          },
          isUpgrade,
        );
        await this.subscriptionService.updateSubscription(
          subscriptionToProcess!.id,
          { status: QISubscriptionStatus.CANCELED, upgradeCredit },
        );
        return;
      case 'paid':
        // Calcular a próxima data de cobrança
        const nextBillingDate = calculateInstallmentDueDate(
          installment + 1,
          subscriptionToProcess!.billingDay,
          subscriptionToProcess!.nextBillingDate.toMillis(),
        );
        // Atualiza o status do pagamento na conta
        this.webhookService.updatePayment(
          account.id!,
          body,
          {
            status: 'paid',
            error_message: '',
            gateway: Gateway.PAGARME,
            subscription_id: subscriptionToProcess!.id,
            planId: subscriptionToProcess!.planId,
          },
          isUpgrade,
        );

        // Atualiza o status da assinatura
        if (isUpgrade) {
          // Aplicando o upgrade immediatamente
          await this.subscriptionService.updateSubscriptionStatus(
            currentSubscription!.id,
            QISubscriptionStatus.EXPIRED,
          );
          await this.subscriptionService.updateSubscription(
            subscriptionToProcess.id,
            {
              status: QISubscriptionStatus.ACTIVE,
              upgradeCredit: upgradeCredit,
              nextBillingDate: toFirebaseTimestamp(nextBillingDate),
            },
          );
        } else {
          if (metadata?.isRenewal) {
            // Renovação de assinatura
            const installments = subscriptionToProcess!.installments;
            const isYearly =
              subscriptionToProcess!.billingInterval ===
              QIBillingInterval.YEARLY;
            const currentPeriodEnd = isYearly
              ? addYears(
                subscriptionToProcess!.currentPeriodEnd.toDate().getTime(),
                1,
              )
              : addDays(
                subscriptionToProcess!.currentPeriodEnd.toDate().getTime(),
                30,
              );
            const nextBillingDate = isYearly
              ? installments > 1
                ? calculateYearlyBillingDate(
                  subscriptionToProcess!.billingDay,
                  currentPeriodEnd,
                )
                : currentPeriodEnd
              : addDays(currentPeriodEnd, 30);
            await this.subscriptionService.updateSubscription(
              subscriptionToProcess!.id,
              {
                cycle: subscriptionToProcess!.cycle + 1,
                currentPeriodStart: subscriptionToProcess!.currentPeriodEnd,
                currentPeriodEnd:
                  toFirebaseTimestamp(currentPeriodEnd),
                nextBillingDate:
                  toFirebaseTimestamp(nextBillingDate),
                status: QISubscriptionStatus.ACTIVE,
                upgradeCredit: upgradeCredit,
              },
            );
            return;
          } else {
            await this.subscriptionService.updateSubscription(
              subscriptionToProcess!.id,
              {
                status: QISubscriptionStatus.ACTIVE,
                upgradeCredit: upgradeCredit,
                nextBillingDate: toFirebaseTimestamp(nextBillingDate),
              },
            );
          }
        }

        // Atualiza os dados da conta com os dados da assinatura
        this.accountService.updateFromSubscription(
          account.id!,
          subscriptionToProcess!,
        );
        break;
      case 'pending':
        if (!last_transaction) return;
        if (installment > 1) {
          Logger(`Webhook pending ignorado -  Parcela ${installment} de ${subscriptionToProcess!.installments} ignorada`);
          return;
        }
        const status = {};
        switch (subscriptionToProcess?.paymentMethod) {
          case QIPaymentMethod.PIX:
            status[isUpgrade ? 'payment_upgrade_status' : 'payment_status'] = {
              ...(isUpgrade ? account.payment_upgrade_status : account.payment_status),
              error_message: '',
              gateway: Gateway.PAGARME,
              status: 'pending',
              subscription_id: subscriptionToProcess.id,
              pix: {
                qrCode: last_transaction.qr_code,
                url: last_transaction.qr_code_url,
                expiresAt: last_transaction.expires_at,
                amount: last_transaction.amount,
                identifier: last_transaction.id,
              },
            };
            await this.accountService.updateAccount(account.id!, status);
            break;
          case QIPaymentMethod.BOLETO:
            await this.accountService.updateAccount(account.id!, {
              payment_status: {
                ...account.payment_status,
                gateway: Gateway.PAGARME,
                status: 'pending_payment',
                error_message: '',
                subscription_id: subscriptionToProcess.id,
                planId: subscriptionToProcess.planId,
              },
            });
            break;
          default:
            break;
        }
        return;
      default:
        return;
    }

    const email = body.data.customer.email;
    if (!email) return;

    const userToken = await this.webhookService.generateCustomToken(
      account.owner,
    );

    const planName = isUpgrade
      ? subscriptionToProcess?.items[0].name
      : currentSubscription?.items[0].name;
    const startAt = isUpgrade
      ? subscriptionToProcess.startDate.toDate()
      : currentSubscription?.startDate.toDate();
    const nextBillingAt = isUpgrade
      ? subscriptionToProcess.nextBillingDate.toDate()
      : currentSubscription?.nextBillingDate.toDate();
    const url = `${process.env.APP_URL}/app/login?token=${userToken}`;

    if (!planName || !startAt || !nextBillingAt || !userToken || !email) return;

    this.webhookService.sendMail(
      email,
      'QI PLUS - Parabéns! Assinatura ativada com sucesso!',
      PaymentUpgrade(
        formatForEmail(startAt.getTime()),
        formatForEmail(nextBillingAt.getTime()),
        planName,
        url,
      ),
      'folder_QIPlus_WEB_ok.pdf',
    );
  }
}

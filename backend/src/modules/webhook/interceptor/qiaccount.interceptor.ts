import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { Account } from 'src/modules/account/model/account.model';
import { FirebaseService } from 'src/modules/firebase/firebase.service';
import { SubscriptionService } from 'src/modules/subscription/subscription.service';
import { QISubscription } from 'src/modules/subscription/types/qiplus.types';

export interface QiAccountRequest extends Request {
  account: Account;
  subscriptionToProcess: QISubscription | null; // Assinatura que está sendo processada
  currentSubscription: QISubscription | null; // Assinatura ativa do usuário
}

@Injectable()
export class QiAccountInterceptorRequest implements CanActivate {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest<QiAccountRequest>();
      const accountId = this.extractBodyAccountId(request);
      Logger.log('Account ID:', accountId);

      if (accountId) {
        const { data: account } =
          await this.firebaseService.getAccountByID(accountId);

        if (account) {
          request['account'] = account;
          Logger.log('Added body [account] successfully.');

          const currentSubscription =
            await this.subscriptionService.getActiveSubscription(account.id!);
          if (currentSubscription) {
            request['currentSubscription'] = currentSubscription;
            Logger.log('Added body [currentSubscription] successfully.');
          }
          const subscriptionId = this.extractBodySubscriptionId(request);
          if (subscriptionId && subscriptionId !== currentSubscription?.id) {
            const subscriptionToProcess =
              await this.subscriptionService.getSubscription(subscriptionId);
            if (subscriptionToProcess) {
              request['subscriptionToProcess'] = subscriptionToProcess;
              Logger.log('Added body [subscriptionToProcess] successfully.');
            }
          } else if (
            subscriptionId &&
            subscriptionId === currentSubscription?.id
          ) {
            request['subscriptionToProcess'] = currentSubscription;
            Logger.log('Added body [subscriptionToProcess] successfully.');
          }
          return true;
        }
      }

      Logger.log('Account not found.');
      return true;
    } catch (error) {
      Logger.log('Account not found.');
      return true;
    }
  }

  private extractBodyAccountId(request: Request): string | null {
    const { metadata } = request.body?.data || {};
    return metadata?.accountId || metadata?.account_id || null;
  }

  private extractBodySubscriptionId(request: Request): string | null {
    const { metadata } = request.body?.data || {};
    return metadata?.subscriptionId || metadata?.subscription_id || null;
  }
}

@Injectable()
export class QiAccountInterceptorBody extends QiAccountInterceptorRequest {
  constructor(
    firebaseService: FirebaseService,
    subscriptionService: SubscriptionService,
  ) {
    super(firebaseService, subscriptionService);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    super.canActivate(context);
    const request = context.switchToHttp().getRequest<QiAccountRequest>();

    if (request.account) {
      request.body['account'] = request.account;

      if (request.subscriptionToProcess) {
        request.body['subscriptionToProcess'] = request.subscriptionToProcess;
      }

      if (request.currentSubscription) {
        request.body['currentSubscription'] = request.currentSubscription;
      }
    }

    return true;
  }
}

import { Injectable } from '@nestjs/common';
import { DefaultResponse } from 'src/modules/firebase/types/response.type';
import { PagarmeService } from 'src/modules/pagarme/pagarme.service';
import { PagarmeSplit } from 'src/modules/pagarme/types/pagarme.split.type';
import { ResponseUtil } from 'src/utils/response';
import { AffiliateRepository } from './affiliate.repository';

@Injectable()
export class AffiliateService {
  constructor(
    private readonly affiliateRepository: AffiliateRepository,
    private readonly pagarmeService: PagarmeService,
  ) {}

  async getAllAffiliates() {
    return this.pagarmeService.listRecipients({ page: 1, size: 10 });
  }

  /**
   * Function to register an recipient in affiliate
   *
   * A função salva o recipient (retorno da pagarme) no affiliate (collection affiliates)
   * @returns void
   */
  async registerAffiliate(
    affiliateId: string,
    gateway: string,
    recipient: any,
  ): Promise<any> {
    this.affiliateRepository.saveRecipient(affiliateId, gateway, recipient);
  }

  /**
   * Function to get affiliate by id
   *
   * A função pega o affiliate pelo id (collection affiliates)
   * @returns affiliate
   * */
  async getAffiliate(
    id: string,
  ): Promise<FirebaseFirestore.DocumentData | null> {
    return this.affiliateRepository.find(id);
  }

  async makeSubscriptionSplit(
    gateway: string,
    affiliateId: string,
  ): Promise<DefaultResponse<PagarmeSplit | null>> {
    const affiliate = await this.getAffiliate(affiliateId);
    if (!affiliate) return ResponseUtil.error('Affiliate not found');

    const recipient = affiliate.recipient[gateway];
    if (!recipient) return ResponseUtil.error('Recipient not found');

    const affiliateAmount = Number(affiliate.data.commission || 0);
    if (affiliateAmount <= 0)
      return ResponseUtil.error('Affiliate comission not found');

    const qiPagarmeRecipientId = process.env.PAGARME_RECIPIENT_ID;
    if (!qiPagarmeRecipientId)
      return ResponseUtil.error('QI Recipient not found in env');

    const qiAmount = 100 - affiliateAmount;

    if (qiAmount + affiliateAmount !== 100)
      return ResponseUtil.error('Total amount must be 100%');

    const split = {
      enabled: true,
      rules: [
        {
          amount: qiAmount,
          recipient_id: qiPagarmeRecipientId,
          type: 'percentage',
          options: {
            charge_processing_fee: true,
            charge_remainder_fee: true,
            liable: true,
          },
        },
        {
          amount: affiliateAmount,
          recipient_id: recipient.id,
          type: 'percentage',
          options: {
            charge_processing_fee:
              process.env.AFFILIATE_PROCESSING_FEE === 'true',
            charge_remainder_fee:
              process.env.AFFILIATE_REMAINDER_FEE === 'true',
            liable: process.env.AFFILIATE_LIABLE === 'true',
          },
        },
      ],
    } as PagarmeSplit;

    return ResponseUtil.success<PagarmeSplit>('Split created', split);
  }
}

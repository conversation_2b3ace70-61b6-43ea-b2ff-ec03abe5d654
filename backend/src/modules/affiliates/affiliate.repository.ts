import { Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';

@Injectable()
export class AffiliateRepository {
  private firestore = admin.firestore();
  private collection = this.firestore.collection('affiliates');

  async find(id: string): Promise<FirebaseFirestore.DocumentData | null> {
    const doc = await this.collection.doc(id).get();
    return doc.data() || null;
  }

  async findAll(): Promise<FirebaseFirestore.DocumentData[]> {
    const snapshot = await this.collection.get();
    return snapshot.docs.map((doc) => doc.data());
  }

  async saveRecipient(affiliateId: string, gateway: string, recipient: any) {
    const affiliate = await this.collection.doc(affiliateId).get();
    if (!affiliate.exists) return { message: 'Affiliate not found' };

    const affiliateData = affiliate.data();

    const data = {
      ...affiliateData,
      recipient: {
        ...affiliateData?.recipient,
        [gateway]: recipient,
      },
    };

    return await this.collection.doc(affiliateId).set(data);
  }
}

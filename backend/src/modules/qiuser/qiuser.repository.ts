import { Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { RegisterDto } from 'src/modules/auth/dto/register.dto';
import { DefaultResponse } from 'src/modules/firebase/types/response.type';
import { ResponseUtil } from 'src/utils/response';

@Injectable()
export class QiUserRepository {
  private firestore = admin.firestore();
  private collection = this.firestore.collection('qiusers');

  async create(
    uid: string,
    accountId: string,
    avatar: string,
    dto: RegisterDto,
  ): Promise<DefaultResponse<admin.firestore.DocumentData | null>> {
    const { name, email, mobile, mobileCC, address, cpf, company, birthdate } =
      dto;
    const [firstName = '', lastName = ''] = name.split(' ');
    const qiUser = {
      id: uid,
      accountId,
      status: 'active',
      author: uid,
      avatar,
      birthdate,
      collection: 'qiusers',
      owner: uid,
      displayName: name,
      firstName,
      lastName,
      mobile,
      mobileCC,
      email,
      type: dto.isCompany ? 'corporation' : 'individual',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      roles: ['owner'],
      dontShowModalShotX: !dto.additionals.includes('shotx-modules'),
      address: {
        city: address.city,
        state: address.state,
        country: address.country,
        street: address.street,
        number: address.number,
        neighborhood: address.neighborhood,
        postalCode: address.postalCode,
        complement: address.complement,
      },
      cpf,
      cnpj: company.cnpj,
      companyName: company.name,
      phone: company.phone,
      phoneCC: company.phoneCountryCode,
      companyEmail: company.email,
      uid,
    };

    const qiUserRef = this.collection.doc(uid);
    const result = await qiUserRef.set(qiUser, { merge: true });
    if (result) {
      const doc = await this.collection.doc(uid).get();
      return ResponseUtil.success('QiUser created', doc);
    }

    return ResponseUtil.error('Error creating QiUser');
  }

  async findById(id: string): Promise<FirebaseFirestore.DocumentData | null> {
    const doc = await this.collection.doc(id).get();
    return doc.data() || null;
  }

  async findByUid(uid: string): Promise<FirebaseFirestore.DocumentData | null> {
    const data = await this.collection.where('uid', '==', uid).limit(1).get();
    if (data.empty) {
      return null;
    }
    return data.docs[0].data() || null;
  }
}

@import "slick-carousel/slick/slick.css";
@import "slick-carousel/slick/slick-theme.css";

@tailwind base;
@tailwind components;
@tailwind utilities;
/* slick-carousel */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 262 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 84% 4.9%;
    --radius: 0.5rem;
  }

  /* Tema Escuro */
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 217.2 32.6% 17.5%;
    --card-foreground: 210 40% 98%;
    --popover: 217.2 32.6% 17.5%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 262 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;
    --muted: 222.2 47.4% 11.2%;
    --muted-foreground: 215.4 16.3% 65%;
    --accent: 262 47.4% 11.2%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 84% 4.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.checkout-gradient {
  background: linear-gradient(135deg, #9b87f5 0%, #7e69ab 100%);
}

.plan-card {
  @apply relative p-6 rounded-lg border border-border transition-all duration-200 hover:shadow-lg;
}

.plan-card.popular {
  @apply border-primary shadow-md;
}

.progress-step {
  @apply flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium;
}

.progress-step.active {
  @apply border-green-500 bg-green-500 text-white;
}

.progress-step.completed {
  @apply border-green-500 bg-green-500 text-white;
}

.progress-line {
  @apply flex-1 h-0.5 bg-border;
}

.progress-line.active {
  @apply bg-green-500;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@layer components {
  .slider-thumb {
    @apply w-5 h-5 bg-blue-500 rounded-full shadow-lg transition-transform hover:scale-110;
  }

  .slider-track {
    @apply h-2 bg-blue-200 rounded-full;
  }
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(53, 53, 53, 0.6) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  transition: width 0.2s ease;
}

.custom-scrollbar:hover::-webkit-scrollbar {
  width: 10px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(53, 53, 53, 0.6);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(53, 53, 53, 0.8);
  width: 10px;
}

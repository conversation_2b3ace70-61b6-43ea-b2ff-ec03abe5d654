import { CheckoutFormData } from '@/components/checkout/types';
import { auth, signInWithToken } from '@/config/firebase';
import { useToast } from '@/hooks/use-toast';
import { parseUserData } from '@/lib/user.utils';
import { authToken, setAuthToken } from '@/services/api';
import { User } from 'firebase/auth';
import { useCallback, useEffect, useState } from 'react';
import { AuthProviderProps } from './auth-context-types';
import { AuthContext } from './useAuth';
import { authService } from '@/services/auth';
import { QISubscription } from '@/types/backend/qiplus.types';
import { subscriptionService } from '@/services/subscription';

export function AuthProvider({ children }: AuthProviderProps) {
  // Autenticação e dados do usuario
  // Inicialmente, o estado de autenticação é indeterminado (undefined)
  const [user, setUser] = useState<User | null | undefined>(undefined);
  const [userData, setUserData] = useState<Partial<CheckoutFormData> | null>(null);
  const [account, setAccount] = useState(null);
  const [accessSystemUrl, setAccessSystemUrl] = useState<string | null>(null);
  const [subscription, setSubscription] = useState<QISubscription | null>(null);
  const [isRegistering, setIsRegistering] = useState(false);

  const displayName = userData?.name || account?.title?.split(' - ')[0] || '';

  const shortenDisplayName = () => {
    if (displayName) {
      const names = displayName.split(' ');
      const formattedNames = names.map((name) => name.charAt(0).toUpperCase() + name.slice(1));
      return formattedNames.join(' ');
    }
    return '';
  };

  const { toast } = useToast();

  const init = useCallback(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');

    if (!user) {
      ['parentId', 'affiliateId'].forEach((key) => {
        if (urlParams.has(key)) {
          localStorage.setItem(key, urlParams.get(key));
        }
      });
    }

    // Remover o token da URL
    window.history.replaceState({}, document.title, window.location.pathname);

    if (token && !user) {
      return loginWithToken(token);
    }
  }, [user]);

  const authenticate = useCallback(async () => {
    if (!user) {
      return null;
    }
    const idToken = await user.getIdToken();
    return authService
      .login(idToken)
      .then((response) => {
        const parsedUserData = parseUserData(response.data.userData);
        if (parsedUserData) {
          setUserData(parsedUserData);
        }
        return response.data;
      })
      .catch((error) => {
        console.log(error);
        return null;
      });
  }, [user]);

  useEffect(() => {
    // Configurar o listener de autenticação do Firebase
    const unsubscribe = auth.onAuthStateChanged((authUser) => {
      // Atualizar o estado do usuário (pode ser null se não estiver autenticado)
      setUser(authUser);

      if (!!authUser) {
        // Se estiver autenticado, atualizar o token de autenticação
        authUser.getIdToken().then((token) => {
          setAuthToken(token);
        });
        // Se estiver autenticado, limpar parâmetros de afiliados do localStorage
        ['parentId', 'affiliateId'].forEach((key) => {
          if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
          }
        });
      } else {
        // Se não estiver autenticado, limpar dados da conta
        setAccount(null);
      }
    });

    const unsubscribeToken = auth.onIdTokenChanged((user) => {
      // Se o usuário estiver autenticado, atualizar o token de autenticação
      if (user) {
        user.getIdTokenResult(true).then((result) => {
          // Atualizar o token de autenticação
          setAuthToken(result.token);
        });
      } else {
        setAuthToken(null);
      }
    });

    const expirationTimeout = setTimeout(() => {
      auth.currentUser?.getIdToken(true).then((token) => {
        setAuthToken(token);
      });
    }, 1000 * 60 * 3); // 3 minutos

    // Inicializar com token da URL se disponível
    init();

    // Limpar o listener quando o componente for desmontado
    return () => {
      unsubscribe();
      unsubscribeToken();
      clearTimeout(expirationTimeout);
    };
  }, [init]);

  useEffect(() => {
    // Só autenticamos se o usuário estiver definido e não for null
    if (user === undefined) {
      return; // Estado inicial, ainda não sabemos se o usuário está autenticado
    }

    if (user) {
      // Usuário está autenticado, obter dados adicionais
      authenticate().then((response) => {
        if (!response || !response.token) {
          toast({
            title: 'Erro ao obter token',
            description: 'Não foi possível obter o token de autenticação.',
            variant: 'destructive',
            position: 'top-right',
          });
          return;
        }
        setAccount(response.account);
        setSubscription(response.subscription);
        setAccessSystemUrl(`${import.meta.env.VITE_SYSTEM_URL_REDIRECT}?token=${response.token}`);
      });
    }
  }, [user, authenticate, toast]);

  const register = async (data) => {
    setIsRegistering(true);
    return authService
      .register(data)
      .then((response) => {
        setIsRegistering(false);
        return {
          error: false,
          message: response.data.message,
          statusCode: response.data.statusCode,
          token: response.data.token,
          uid: response.data.uid,
          accountId: response.data.accountId,
          qiUserId: response.data.qiUserId,
        };
      })
      .catch((error) => {
        console.log(error);
        setIsRegistering(false);
        return {
          error: true,
          message: error.response.data.message,
          statusCode: error.response.status,
          token: null,
          uid: null,
          accountId: null,
          qiUserId: null,
        };
      });
  };

  const loginWithToken = async (token) => {
    try {
      const userCredential = await signInWithToken(token);
      const user = userCredential.user;
      setUser(user);
      return user;
    } catch (error) {
      console.log(error);
      return null;
    }
  };

  const signOut = async () => {
    await auth.signOut();
    setUser(null);
    setAccount(null);
    setAccessSystemUrl(null);
  };

  const getUserData = (field: string) => {
    if (!hasUserData(field)) {
      return null;
    }
    return userData?.[field] || null;
  };

  const hasUserData = (field: string) => {
    const hasField = Object.keys(userData || {}).includes(field);
    return hasField && userData?.[field] !== undefined;
  };

  // Determinar se o usuário está autenticado
  // - undefined: estado inicial, ainda não sabemos
  // - true: usuário autenticado
  // - false: usuário não autenticado
  const isAuthenticated = user === undefined ? undefined : !!user;

  // Determinar se estamos carregando
  // - true: se o usuário é undefined (estado inicial) ou se estamos carregando dados adicionais
  // - false: caso contrário
  const isLoading = user === undefined || (isAuthenticated && !authToken);

  const getSubscription = async () => {
    if (!account) {
      return null;
    }
    return subscriptionService.getSubscription(account.id);
  };

  const refreshSubscription = async () => {
    const subscription = await getSubscription();
    setSubscription(subscription);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        userData,
        getUserData,
        hasUserData,
        account,
        displayName,
        shortenDisplayName,
        setAccount,
        register,
        isAuthenticated,
        authenticate,
        accessSystemUrl,
        signOut,
        loginWithToken,
        loading: isLoading,
        subscription,
        setSubscription,
        getSubscription,
        refreshSubscription,
        isRegistering,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

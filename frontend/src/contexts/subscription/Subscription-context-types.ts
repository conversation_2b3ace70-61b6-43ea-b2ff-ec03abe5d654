import { QIScheduledAction } from "@/types/backend/qiplus.types";
import { ReactNode } from "react";

export interface SubscriptionContextData {
  subscription: any;
  setSubscription: (subscription: any) => void;
  subscriptionHistory: any[];
  setSubscriptionHistory: (subscriptionHistory: PaymentHistoryItem[]) => void;
  updateSubscriptionScheduledAction: (id: string, scheduledAction: QIScheduledAction) => void;
}

export interface SubscriptionProviderProps {
  children: ReactNode;
}

export interface PaymentHistoryItem {
  isUpgrade: any;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  items: any[];
  paymentMethod: string;
  next_billing_at: string;
  plan: {
    name: string;
  };
  status: string;
  invoiceUrl?: string;
  id: string;
  isCurrent: boolean;
  upgradeTo: string;
  installments: number;
  scheduledAction?: QIScheduledAction;
}
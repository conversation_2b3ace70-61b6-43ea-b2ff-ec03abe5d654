import { QIPaymentMethod } from "@/types/backend/qiplus.types";
import { calculateDaysUntilBillingSimple, currentTimestamp, nowDate } from "./date.utils";
import { PaymentMethod } from "@/components/checkout/types";

/**
 * Calculate the amount for the first installment and the remaining installments for yearly subscriptions
 * @param totalAmount The total amount of the subscription
 * @param installments The number of installments
 * @param billingDay The day of the month for billing
 * @param creditDiscount The credit discount to apply
 * @param endDate The end date of the subscription
 * @param today Optional date to use instead of the current date (useful for testing)
 * @returns The amount for the first installment and the remaining installments
 */
export function calculateYearlyInstallmentsAmount({
  totalAmount,
  installments,
  billingDay,
  creditDiscount,
  paymentMethod,
  today,
  endDate,
  daysInCycle,
}: {
  totalAmount: number,
  installments: number,
  billingDay: number,
  creditDiscount: number,
  paymentMethod?: PaymentMethod | QIPaymentMethod,
  today?: number,
  endDate: number,
  daysInCycle?: number,
}) {

  if (installments === 1) {
    return [totalAmount, 0];
  }

  if (paymentMethod === PaymentMethod.CREDIT_CARD) {
    const perInstallment = Math.round(totalAmount / installments);
    return [perInstallment, perInstallment];
  }

  today ??= currentTimestamp();

  daysInCycle ??= Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));

  const daysUntilBilling = calculateDaysUntilBillingSimple(today, billingDay);

  const amountPerDay = totalAmount / daysInCycle;

  let firstInstallmentAmount = daysUntilBilling > 0
    ? amountPerDay * daysUntilBilling
    : (amountPerDay * daysInCycle) / installments;

  const amountPerInstallment = totalAmount / installments;

  const remainingCredit = Math.max(0, creditDiscount - firstInstallmentAmount);

  const remainingInstallmentAmount = amountPerInstallment - (remainingCredit / (installments - 1));

  // If credit is enough to cover the first installment, return 0 for the first installment and the rest of the credit for the remaining installments
  if (creditDiscount >= firstInstallmentAmount) {
    return [0, Math.round(remainingInstallmentAmount)];
  }

  return [Math.round(firstInstallmentAmount), Math.round(remainingInstallmentAmount)];
}

/**
 * Calculate the next billing date for yearly subscriptions based on the billing day
 * @param billingDay The day of the month for billing
 * @param currentDateOverride Optional date to use instead of the current date (useful for testing)
 * @returns The next billing date
 */
export function calculateYearlyBillingDate(billingDay: number, currentDateOverride?: number): number {
  // Use provided date or current date
  const currentDate = nowDate(currentDateOverride);

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const currentDay = currentDate.getDate();

  // Create a date for the billing day in the current month
  const nextBillingDate = new Date(year, month, billingDay);

  // If the billing day is in the past (less than current day), move to next month
  if (billingDay < currentDay) {
    nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
  }

  return nextBillingDate.getTime();
}
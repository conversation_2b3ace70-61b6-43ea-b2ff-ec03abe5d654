import { CheckoutFormData } from "@/components/checkout/types";
import { QIUser } from "@/types/qiuser.type";
import { parsePhone } from "./phone.utils";
import { maskCNPJ, maskCPF } from "./utils";

export const parseUserData = (userData: QIUser): Partial<CheckoutFormData> => {

    const phone = parsePhone(`${userData.mobileCC} ${userData.mobile}`);
    const companyPhone = parsePhone(`${userData.phoneCC} ${userData.phone}`);
    const getBirthDate = () => {
        if (userData.birthdate) {
            return userData.birthdate;
        }

        if (userData.birth) {
            const { day, month, year } = userData?.birth || {};
            return day && month && year ? `${year}-${month}-${day}` : '';
        }
        return '';
    }
    return {
        name: userData.displayName,
        email: userData.email,
        cpf: maskCPF(userData.cpf),
        phone: phone?.formatNational || '',
        phoneCountryCode: phone?.countryCodePlus || '+55',
        birthdate: getBirthDate(),
        isCompany: !!userData.cnpj,
        companyName: userData.companyName,
        companyCnpj: maskCNPJ(userData.cnpj),
        companyEmail: userData.companyEmail,
        companyPhone: companyPhone?.formatNational || '',
        companyPhoneCountryCode: companyPhone?.countryCodePlus || '+55',
        city: userData.address.city,
        complement: userData.address.complement,
        country: userData.address.country,
        neighborhood: userData.address.neighborhood,
        number: userData.address.number,
        postalCode: userData.address.postalCode,
        state: userData.address.state,
        street: userData.address.street,
    }

};

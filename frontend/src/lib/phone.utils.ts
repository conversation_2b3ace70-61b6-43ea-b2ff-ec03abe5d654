import {
  E164Number,
  NationalNumber,
  parsePhoneNumberFromString,
} from "libphonenumber-js";

interface PhoneNumberInfo {
  countryCode: string;
  countryCodePlus: string;
  national: string;
  international: string;
  formatNational: string;
  formatInternational: string;
  country: string;
}

export function parsePhone(
  phone: string,
  removePlus = false
): PhoneNumberInfo | undefined {

  if (!phone.includes("+")) {
    phone = `+${phone}`;
  }
  const phoneNumber = parsePhoneNumberFromString(phone);

  if (!phoneNumber) {
    return;
  }

  const countryCode = phoneNumber.countryCallingCode; // Código do país
  let national = phoneNumber.nationalNumber; // Número local (sem o código do país)
  let international = phoneNumber.number; // Número formatado internacionalmente
  let formatNational = phoneNumber.formatNational(); // Número local (sem o código do país)
  let formatInternational = phoneNumber.formatInternational(); // Número local (sem o código do país)
  const country = phoneNumber.country || "Unknown"; // País correspondente ao código

  // Verificando 9º Digito
  if (country === "BR" || countryCode == "55") {
    if (national.length < 11) {
      const phone = national.slice(2);
      national = national.replace(phone, `9${phone}`) as NationalNumber;
      international = international.replace(phone, `9${phone}`) as E164Number;
      formatNational = formatNational.replace(phone, `9${phone}`);
      formatInternational = formatInternational.replace(phone, `9${phone}`);
    }
  }

  // Removendo o `+`
  if (removePlus) {
    international = international.slice(1) as E164Number;
    formatInternational = formatInternational.slice(1);
  }

  const countryCodePlus = `+${countryCode}`;

  return {
    countryCode,
    countryCodePlus,
    national,
    international,
    formatNational,
    formatInternational,
    country,
  };
}

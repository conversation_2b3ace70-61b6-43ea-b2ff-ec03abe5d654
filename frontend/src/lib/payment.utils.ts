import {
  BoletoSubscription,
  CreditCardSubscription,
  Subscription,
} from "@/models/Subscription";

interface Metadata {
  [key: string]: string; // ou mais específico se você souber os tipos exatos
}

interface DataItem {
  name: string;
  value: number;
}

export function processSubscriptionResponse(data): Subscription {
  switch (data.payment_method) {
    case "boleto":
      return new BoletoSubscription(data);
    case "credit_card":
      return new CreditCardSubscription(data);
    default:
      return new Subscription(data);
  }
}

export function extractDataFromMetadata(metadata: Metadata): DataItem[] {
  // Defina um "map" que relaciona a chave do metadata com o nome desejado no array final
  const fieldsMap = [
    { metadataKey: "valor de Leads adicionais", label: "Leads adicionais" },
    { metadataKey: "shotxValue", label: "Shotx" },
    { metadataKey: "valores adicionais", label: "Valores adicionais" },
  ];

  const data: DataItem[] = [];

  fieldsMap.forEach(({ metadataKey, label }) => {
    // Verifica se a chave existe no metadata
    if (metadataKey in metadata) {
      // Tenta converter para número
      const parsedValue = parseFloat(metadata[metadataKey]);
      // Adiciona ao array somente se for um número válido e maior que 0
      if (!isNaN(parsedValue) && parsedValue > 0) {
        data.push({
          name: label,
          value: parsedValue,
        });
      }
    }
  });

  return data;
}

// utils/date.ts - FRONTEND
import { format, formatDistance, isValid, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Obtém o timestamp atual em milissegundos
 * @returns timestamp atual como number
 */
export function currentTimestamp(): number {
    return Date.now();
}

/**
 * Obtém a data atual
 * @returns Date object atual
 */
export function nowDate(dateString?: string | number): Date {
    return new Date(dateString || Date.now());
}

/**
 * Converte timestamp para Date object
 * @param timestamp - timestamp em milissegundos
 * @returns Date object ou null se inválido
 */
export function toDate(timestamp: number): Date | null {
    const date = new Date(timestamp);
    return isValid(date) ? date : null;
}

/**
 * Formata timestamp para exibição completa
 * @param timestamp - timestamp em milissegundos
 * @param formatStr - formato personalizado (opcional)
 * @returns string formatada ou 'Data inválida'
 */
export function formatFull(timestamp: number, formatStr: string = 'dd/MM/yyyy HH:mm'): string {
    const date = toDate(timestamp);
    if (!date) return 'Data inválida';

    return format(date, formatStr, { locale: ptBR });
}

/**
 * Formata timestamp para exibição de data apenas
 * @param timestamp - timestamp em milissegundos
 * @returns string formatada (ex: "15/03/2024")
 */
export function formatDate(timestamp: number): string {
    return formatFull(timestamp, 'dd/MM/yyyy');
}

/**
 * Formata timestamp para exibição de hora apenas
 * @param timestamp - timestamp em milissegundos
 * @returns string formatada (ex: "14:30")
 */
export function formatTime(timestamp: number): string {
    return formatFull(timestamp, 'HH:mm');
}

/**
 * Formata timestamp para exibição relativa (ex: "há 2 horas")
 * @param timestamp - timestamp em milissegundos
 * @returns string formatada relativamente ao momento atual
 */
export function formatRelative(timestamp: number): string {
    const date = toDate(timestamp);
    if (!date) return 'Data inválida';

    return formatDistance(date, new Date(), {
        addSuffix: true,
        locale: ptBR
    });
}

/**
 * Formata para input datetime-local do HTML
 * @param timestamp - timestamp em milissegundos
 * @returns string no formato "YYYY-MM-DDTHH:mm" ou ''
 */
export function formatForInput(timestamp: number): string {
    const date = toDate(timestamp);
    if (!date) return '';

    return format(date, "yyyy-MM-dd'T'HH:mm");
}

/**
 * Converte input datetime-local para timestamp
 * @param inputValue - valor do input (YYYY-MM-DDTHH:mm)
 * @returns timestamp em milissegundos ou null se inválido
 */
export function fromInput(inputValue: string): number | null {
    if (!inputValue) return null;

    const date = parseISO(inputValue);
    return isValid(date) ? date.getTime() : null;
}

/**
 * Cria timestamp para uma data específica
 * @param year - ano
 * @param month - mês (1-12)
 * @param day - dia
 * @param hour - hora (opcional, padrão 0)
 * @param minute - minuto (opcional, padrão 0)
 * @returns timestamp em milissegundos
 */
export function create(year: number, month: number, day: number, hour: number = 0, minute: number = 0): number {
    return new Date(year, month - 1, day, hour, minute).getTime();
}

/**
 * Verifica se um timestamp é válido
 * @param timestamp - timestamp a ser validado
 * @returns true se válido
 */
// export function isValid(timestamp: number): boolean {
//     return toDate(timestamp) !== null;
// }

/**
 * Obtém início do dia para um timestamp
 * @param timestamp - timestamp em milissegundos
 * @returns timestamp do início do dia (00:00:00)
 */
export function startOfDay(timestamp: number): number | null {
    const date = toDate(timestamp);
    if (!date) return null;

    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    return startOfDay.getTime();
}

/**
 * Obtém fim do dia para um timestamp
 * @param timestamp - timestamp em milissegundos
 * @returns timestamp do fim do dia (23:59:59.999)
 */
export function endOfDay(timestamp: number): number | null {
    const date = toDate(timestamp);
    if (!date) return null;

    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    return endOfDay.getTime();
}

/**
 * Verifica se uma pessoa é maior de idade
 * @param birthdate - data de nascimento
 * @param age - idade mínima (padrão: 18)
 * @returns true se maior de idade
 */
export function isOver(birthdate: Date, age: number = 18): boolean {
    const minAgeDate = new Date();
    minAgeDate.setFullYear(minAgeDate.getFullYear() - age);
    return birthdate <= minAgeDate;
}

/**
 * Converte uma data para ISO string mantendo o horário local
 * @param date - Date object ou timestamp
 * @returns string ISO com horário local (não UTC)
 */
export function toLocalISOString(date: Date | number): string {
    const dateObj = typeof date === 'number' ? new Date(date) : date;
    return new Date(dateObj.getTime() - dateObj.getTimezoneOffset() * 60000).toISOString();
}

/**
 * Obtém dia do mês a partir de um timestamp
 * @param timestamp - timestamp em milissegundos
 * @returns dia do mês (1-31)
 */
export function getDate(timestamp: number): number {
    return new Date(timestamp).getDate();
}

/**
 * Obtém mês a partir de um timestamp
 * @param timestamp - timestamp em milissegundos
 * @returns mês (0-11, onde 0 é janeiro)
 */
export function getMonth(timestamp: number): number {
    return new Date(timestamp).getMonth();
}

/**
 * Obtém ano a partir de um timestamp
 * @param timestamp - timestamp em milissegundos
 * @returns ano (ex: 2024)
 */
export function getFullYear(timestamp: number): number {
    return new Date(timestamp).getFullYear();
}

/**
 * Adiciona dias a uma data
 * @param date - data base
 * @param days - número de dias a adicionar
 * @returns nova data com os dias adicionados
 */
export function addDays(date: number, days: number): number {
    return date + (days * 24 * 60 * 60 * 1000);
}

/**
 * Adiciona meses a uma data
 * @param date - data base
 * @param months - número de meses a adicionar
 * @returns nova data com os meses adicionados
 */
export function addMonths(date: number, months: number): number {
    const newDate = new Date(date);
    newDate.setMonth(newDate.getMonth() + months);
    return newDate.getTime();
}

/**
 * Adiciona anos a uma data
 * @param date - data base
 * @param years - número de anos a adicionar
 * @returns nova data com os anos adicionados
 */
export function addYears(date: number, years: number): number {
    const newDate = new Date(date);
    newDate.setFullYear(newDate.getFullYear() + years);
    return newDate.getTime();
}

/**
 * Calcula diferença entre dois timestamps em milissegundos
 * @param timestamp1 - primeiro timestamp
 * @param timestamp2 - segundo timestamp
 * @returns diferença em milissegundos
 */
export function diff(timestamp1: number, timestamp2: number): number {
    return Math.abs(timestamp1 - timestamp2);
}

/**
 * Calcula diferença entre dois timestamps em dias
 * @param timestamp1 - primeiro timestamp
 * @param timestamp2 - segundo timestamp
 * @returns diferença em dias
 */
export function diffInDays(timestamp1: number, timestamp2: number): number {
    return diff(timestamp1, timestamp2) / (24 * 60 * 60 * 1000);
}

/**
 * Cria timestamp para uma data específica em UTC
 * @param year - ano
 * @param month - mês (1-12)
 * @param day - dia
 * @param hour - hora (opcional, padrão 0)
 * @param minute - minuto (opcional, padrão 0)
 * @returns timestamp em milissegundos
 */
export function createUTC(year: number, month: number, day: number, hour: number = 0, minute: number = 0): number {
    return Date.UTC(year, month - 1, day, hour, minute);
}

/**
 * Calcula quantos dias faltam para o vencimento
 * @param today - data de referência (ex: data de hoje)
 * @param billingDay - dia do vencimento (1-31)
 * @returns diferença em dias (arredondado para cima)
 */
export function calculateDaysUntilBillingSimple(today: number, billingDay: number): number {
    const currentDay = getDate(today);

    // Se o vencimento ainda não chegou este mês
    if (billingDay > currentDay) {
        return billingDay - currentDay;
    }

    // Se já passou, calcular para próximo mês
    // Adicionar 1 mês ao timestamp atual
    const nextMonth = addMonths(today, 1);

    // Criar data do vencimento no próximo mês
    const nextYear = getFullYear(nextMonth);
    const nextMonthNumber = getMonth(nextMonth) + 1; // +1 porque createUTC espera 1-12
    const nextBillingTimestamp = createUTC(nextYear, nextMonthNumber, billingDay);

    // Retornar diferença em dias (arredondado para cima)
    return Math.ceil(diffInDays(nextBillingTimestamp, today));
}

// Exemplo de uso:
/*
// Obter momento atual
const now = currentTimestamp();

// Formatações para exibição
const fullDate = formatFull(now); // "15/03/2024 14:30"
const dateOnly = formatDate()(now); // "15/03/2024"
const timeOnly = formatTime(now); // "14:30"
const relative = formatRelative(now - 3600000); // "há 1 hora"

// Para inputs HTML
const inputValue = formatForInput(now);
const timestampFromInput = fromInput("2024-03-15T14:30");

// Validação
const isValidDate = isValid(now);

// Criar data específica
const specificDate = create(2024, 3, 15, 14, 30);
*/
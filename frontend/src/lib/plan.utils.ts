import { PlanModel } from "@/models/Plan";
import { QIBillingInterval } from "@/types/backend/qiplus.types";
import { Plan } from "@/types/plan-types";
import { currentTimestamp } from "./date.utils";

// Verifica se os planos são iguais, considerando leads e recursos personalizados
export function isSamePlan(planA: Plan | undefined, planB: Plan | undefined): boolean {
  if (!planA || !planB || planB.uniqueId !== planA.uniqueId) return false;

  return true;
}

export function isPartialUpgrading(currentPlan: Plan | undefined, newPlan: Plan | undefined): boolean {
  if (!currentPlan || !newPlan || newPlan.uniqueId !== currentPlan.uniqueId) return false;

  // Verifica se há mais leads
  if (newPlan.leadsCount > currentPlan.leadsCount) return true;

  // Verifica se há mais recursos personalizados
  const currentTotalFeatures = currentPlan.originalFeatures?.reduce((acc, feature) => acc + feature.quantity, 0);
  const compareTotalFeatures = newPlan.customFeatures?.reduce((acc, feature) => acc + feature.quantity, 0);
  if (compareTotalFeatures > currentTotalFeatures) return true;

  return false;
};

export function getPlanOption(plan: Plan, leadsCount: number) {
  return (
    plan.options.find(
      (option) =>
        leadsCount >= option.contacts_min && leadsCount <= option.contacts_max,
    ) || null
  );
}

/**
 * Get the base price of a plan in cents
 * @param plan Plan object
 * @returns Base price in cents
 */
export function getBasePrice(plan: Plan | undefined) {
  if (!plan) {
    return 0;
  }

  const option = getPlanOption(plan, plan.leadsCount);

  // Convert to cents
  const basePrice = plan.isYearly
    ? Math.round(option?.yearly_value * 12)
    : Math.round(option?.monthly_value);

  return basePrice;
}

/**
 * Get the price of custom features in cents
 * @param plan Plan object
 * @returns Custom features price in cents
 */
export function getCustomFeaturesPrice(plan: Plan | undefined) {
  if (!plan) {
    return 0;
  }

  const customFeaturesPrice = plan.customFeatures.reduce((acc, feature) => {
    const quantity = Math.max(feature.included, Math.min(Infinity, feature.quantity));
    const additionals = quantity - feature.included;

    // Convert to cents
    const priceInCents = plan.isYearly
      ? Math.round(feature.yearlyPrice * 12)
      : Math.round(feature.monthlyPrice);

    return acc + priceInCents * additionals;
  }, 0);

  return customFeaturesPrice;
}

/**
 * Get the total price of a plan in cents
 * @param plan Plan object
 * @returns Total price in cents
 */
export function getTotalPrice(plan: Plan | undefined) {
  if (!plan) {
    return 0;
  }

  return getBasePrice(plan) + getCustomFeaturesPrice(plan);
}

export function getCurrentPlanOnYearly(plans: Plan[], currentPlan: Plan | null) {
  if (!currentPlan) return null;

  const currentPlanYearly = plans.find((plan) => plan.id === currentPlan.id && plan.isYearly);
  if (!currentPlanYearly) return currentPlan;

  currentPlanYearly.customFeatures = currentPlan.customFeatures || [];
  currentPlanYearly.originalFeatures = currentPlan.originalFeatures || [];
  currentPlanYearly.leadsCount = currentPlan.leadsCount;
  currentPlanYearly.isYearly = true;
  return currentPlanYearly;
}

export function getCurrentPlanOnMonthly(plans: Plan[], currentPlan: Plan | null) {
  if (!currentPlan) return null;

  const currentPlanMonthly = plans.find((plan) => plan.id === currentPlan.id && !plan.isYearly);
  if (!currentPlanMonthly) return currentPlan;

  const plan = PlanModel.recriate(currentPlanMonthly);

  plan.customFeatures = currentPlan.customFeatures || [];
  plan.originalFeatures = currentPlan.originalFeatures || [];
  plan.leadsCount = currentPlan.leadsCount;
  plan.isYearly = false;
  return plan;
}


/**
 * Calculate discount between monthly and yearly values in cents
 * @param monthlyValueCents Monthly value in cents
 * @param yearlyValueCents Yearly value in cents
 * @returns Discount amount in cents
 */
export function getDiscount(monthlyValueCents: number, yearlyValueCents: number) {
  return (monthlyValueCents - yearlyValueCents);
}

/**
 * Calculate discount percentage between monthly and yearly values
 * @param monthlyValueCents Monthly value in cents
 * @param yearlyValueCents Yearly value in cents
 * @returns Discount percentage
 */
export function getDiscountPercentage(monthlyValueCents: number, yearlyValueCents: number) {
  return ((monthlyValueCents - yearlyValueCents) / monthlyValueCents) * 100;
}

export function calculateProportionalValue({
  totalAmount,
  billingInterval,
  endDate,
  today,
}: {
  totalAmount: number,
  billingInterval: QIBillingInterval,
  endDate: number,
  today?: number,
}) {
  today ??= currentTimestamp();

  const daysInCycle = billingInterval === QIBillingInterval.YEARLY ? 365 : 30;
  const daysUntilEnd = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
  const amountPerDay = totalAmount / daysInCycle;

  return amountPerDay * daysUntilEnd;
}

/**
 * Interface para os parâmetros de cálculo de upgrade
 */
export interface UpgradeCalculationParams {
  currentPlan: Plan | null;
  selectedPlan: Plan | null;
  subscription: any; // Tipo da subscription do contexto
  subtotal: number;
  discount: number;
  isYearly: boolean;
}

/**
 * Interface para o resultado do cálculo de upgrade
 */
export interface UpgradeCalculationResult {
  isUpgrade: boolean;
  isPartialUpgrade: boolean;
  isDowngrade: boolean;
  changingBillingCycle: boolean;
  upgradeCredit: number;
  finalSubtotal: number;
  total: number;
  shouldApplyProportionalCalculation: boolean;
}

/**
 * Função utilitária para calcular valores de upgrade de forma simplificada
 * Centraliza toda a lógica complexa de upgrade em um local
 */
export function calculateUpgradeValues(params: UpgradeCalculationParams): UpgradeCalculationResult {
  const { currentPlan, selectedPlan, subscription, subtotal, discount, isYearly } = params;
  if (!subscription) return {
    isUpgrade: false,
    isPartialUpgrade: false,
    isDowngrade: false,
    changingBillingCycle: false,
    upgradeCredit: 0,
    finalSubtotal: subtotal,
    total: subtotal,
    shouldApplyProportionalCalculation: false,
  };

  // 1. Identificar tipo de upgrade/downgrade
  const isUpgrade = currentPlan && selectedPlan && currentPlan?.config?.order < selectedPlan?.config?.order;
  const isDowngrade = currentPlan && selectedPlan && currentPlan?.config?.order > selectedPlan?.config?.order;
  const isPartialUpgrade = isPartialUpgrading(currentPlan, selectedPlan);
  const changingBillingCycle = !!currentPlan && currentPlan?.isYearly !== isYearly;

  // 2. Determinar se deve aplicar cálculo proporcional
  const shouldApplyProportionalCalculation = subscription.billingDay !== new Date().getDate();

  // 3. Calcular crédito de upgrade (só se não estiver mudando ciclo de cobrança)
  const upgradeCredit = (isUpgrade || isPartialUpgrade) && !changingBillingCycle && currentPlan?.credit
    ? currentPlan.credit
    : 0;

  // 4. Calcular subtotal final
  let finalSubtotal = subtotal;

  if (shouldApplyProportionalCalculation) {
    const today = new Date();

    const remainingDays = Math.floor(
      (new Date(subscription.endDate).getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
    );

    const newDailyValue = subtotal / 365;

    finalSubtotal = Math.round(remainingDays * newDailyValue);
  }

  // 5. Calcular total final
  const total = finalSubtotal - discount - upgradeCredit;

  return {
    isUpgrade: !!isUpgrade,
    isPartialUpgrade,
    isDowngrade: !!isDowngrade,
    changingBillingCycle,
    upgradeCredit,
    finalSubtotal,
    total: Math.max(0, total), // Garantir que o total nunca seja negativo
    shouldApplyProportionalCalculation,
  };
}
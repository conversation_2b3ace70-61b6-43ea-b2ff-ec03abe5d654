import { Card } from "@/components/ui/card";
import { LiveSupportChat } from "@/components/chat/LiveSupportChat";
import { cn, formatCurrency } from "@/lib/utils";
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  ChevronLeft,
  ChevronRight,
  Eye,
  Loader2,
  User,
} from "lucide-react";
import { subscriptionService } from "@/services/subscription";
import { useEffect, useState } from "react";
import InfoCard, { InfoItem } from "@/components/checkout/InfoCard";
import SliderCard from "@/components/InfoSliderCard";
import React from "react";
import { useNavigate } from "react-router-dom";
import { SubscriptionHeader } from "@/components/SubascriptionHeader";
import { useAuth } from "@/contexts/auth/useAuth";
import { Button } from "@/components/ui/button";
import { QIScheduledAction } from "@/types/backend/qiplus.types";
import { t } from "@/lib/translations.helper";
import PixPayments from "@/components/PixPayments";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { AlertMessage } from "@/components/AlertMessage";
import { ItemCard } from "@/components/ItemCard";
import { useToast } from "@/hooks/use-toast";
import { useSubscription } from "@/contexts/subscription/SubscriptionContext";
import { formatDate } from "@/lib/date.utils";

const ArrowButton = ({
  onClick,
  direction,
}: {
  onClick?: () => void;
  direction: "left" | "right";
}) => (
  <button
    onClick={onClick}
    className={`absolute top-1/2 transform -translate-y-1/2 z-10 ${direction === "left" ? "-left-4" : "-right-4"
      } text-[#0071e2] bg-transparent p-1 hover:opacity-75 dark:text-white`}
  >
    {direction === "left" ? (
      <ChevronLeft size={18} />
    ) : (
      <ChevronRight size={18} />
    )}
  </button>
);

export default function Subscription() {
  const navigate = useNavigate();
  const { loading: isLogging, userData: user } = useAuth();
  const { reactivateSubscription, setCurrentStep } = useCheckout();
  const { subscription, setSubscription, subscriptionHistory, setSubscriptionHistory, updateSubscriptionScheduledAction } = useSubscription();

  const [paymentData, setPaymentData] = useState<any>(null);
  const [increments, setIncrements] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [reactivating, setReactivating] = useState(false);

  // Estados para paginação
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5; // Quantidade de registros por página
  // reactivateSubscription
  const scheduledCancel = subscription?.scheduledAction === "cancel";
  const subscriptionCanceled = subscription?.status === "canceled";
  const isLoading = loading || isLogging;
  const { toast } = useToast();

  const firstName = user?.name?.split(" ")[0];

  const handleReactivate = async () => {
    setReactivating(true);
    try {
      reactivateSubscription().then((updated) => {
        if (updated.scheduledAction !== QIScheduledAction.CANCEL) {
          updateSubscriptionScheduledAction(updated.id, null);
        } else {
          toast({
            title: "Erro ao reativar assinatura",
            description:
              "Algo aconteceu ao tentar reativar sua assinatura, por favor tente mais tarde. Se persistir, entre em contato com o suporte.",
            variant: "destructive",
            duration: 3000,
          });
        }
        setReactivating(false);
      })
    } catch (error) {
      console.error("Error reactivating subscription:", error);
    }
  };

  useEffect(() => {
    if (isLogging) return;
    async function fetchSubscriptionHistory() {
      try {
        const sub = await subscriptionService.getSubscriptions();
        setSubscription(sub || null);
        setSubscriptionHistory(sub?.subscriptions || []);
        setIncrements(sub?.increments || []);
      } catch (error) {
        console.error("Error fetching subscription data:", error);
      } finally {
        setLoading(false);
      }
    }
    fetchSubscriptionHistory();
  }, [isLogging]);


  const sliderSettings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 2,
    slidesToScroll: 1,
    nextArrow: <ArrowButton direction="right" />,
    prevArrow: <ArrowButton direction="left" />,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  const paySubscription = async () => {
    try {
      const invoice = subscription?.invoices.find((invoice) => invoice.status === "pending");
      setPaymentData(invoice);
    } catch (error) {
      console.error("Error paying subscription:", error);
    }
  };

  const goToPlans = () => {
    setCurrentStep(0);
    navigate("/");
  };

  let infoCardData: InfoItem[] = [
    {
      label: "Você não tem um plano",
      value: (
        <div className="w-full flex justify-between">
          <span className="text-sm text-[#0071e2]">
            Escolha agora mesmo o plano perfeito para você
          </span>
          <Button onClick={goToPlans}>Escolher Plano</Button>
        </div>
      ),
      append: null
    },
  ];

  const status = scheduledCancel ? "cancel" : subscription?.status
  const isActive = status === "active";

  if (!!subscription?.next_billing_date) {
    infoCardData = [
      {
        label: "Plano",
        value: subscription?.plan || "Você não tem um plano",
        append: subscription?.interval && <span className="text-xs text-muted-foreground">
          &nbsp;
          ({t(`interval.${subscription?.interval}`)})
        </span>
      },
      {
        label: subscriptionCanceled || scheduledCancel ? "Acesso até" : "Próxima cobrança",
        value: formatDate(subscription?.next_billing_date) || "-",
      },
      {
        label: "Valor",
        value: `${formatCurrency(subscription?.amount || 0)}`,
        append: <span >
          <span className="text-xs text-muted-foreground">
            /{t(`interval.${subscription?.interval}`)}
          </span>
        </span>
      },
      {
        label: "Status",
        value: isActive ? (
          <span className={cn(
            "text-xs font-medium me-2 px-2.5 py-1 rounded-md", status === "active" && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300")}>
            {t(`status.${status}`) || "-"}
          </span>
        ) : (
          <div className="flex items-center gap-2 justify-between">
            <span className="text-red-500">{t(`status.${status}`)}</span>
          </div>
        ),
        append: subscription?.status === "pending" ? (
          <Button onClick={paySubscription}>Pagar Agora</Button>
        ) : null
      },
    ];
  }

  // Cálculo de paginação
  const totalPages = Math.ceil(subscriptionHistory.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentHistoryItems = subscriptionHistory.slice(startIndex, endIndex);

  const getTotal = (items: any[]) => {
    return items.reduce((acc, item) => acc + item.totalPrice, 0);
  };

  return (
    <div className="min-h-screen mb-[5rem] px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <SubscriptionHeader />

        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Gerenciar Assinatura</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Gerencie sua assinatura atual e veja o histórico de pagamentos
          </p>
        </div>

        {/* Welcome Message - 'Olá, {user.name}. Aqui você pode gerenciar sua assinatura.' */}
        {firstName && (
          <AlertMessage
            variant="default"
            title={`Olá, ${firstName}. Aqui você pode gerenciar sua assinatura.`}
            message={[
              'Aqui você pode visualizar o status da sua assinatura, gerenciar os pagamentos e cancelar ou reativar a assinatura, se necessário.',
              'Caso precise de ajuda, entre em contato conosco através do chat ao lado.'
            ]}
            action={null}
            icon={<User />}
          />
        )}

        {/* Voltar Button */}
        <div className="mb-6">
          <Button
            type="button"
            onClick={() => window.history.back()}
            variant="ghost"
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
        </div>

        {subscription?.scheduledAction === "cancel" && (
          <AlertMessage
            variant="destructive"
            title={`Sua assinatura será cancelada em ${formatDate(subscription?.next_billing_date)}, conforme solicitado.`}
            message={[
              '🔒 Fique tranquilo(a), nenhum valor adicional será cobrado, e você continuará tendo acesso aos benefícios do plano até essa data.',
              '💬 Se mudar de ideia ou precisar de ajuda com outro plano, estaremos à disposição para ajudar. Agradecemos por ter feito parte da nossa jornada!'
            ]}
            action={reactivating ? (
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={handleReactivate}
              >
                Manter assinatura
              </Button>
            )}
          />
        )}

        <div className="mx-auto grid grid-cols-1 md:grid-cols-2 gap-4">
          {isLoading ? (
            <>
              <div className="animate-pulse p-4 bg-gray-200 rounded-lg">
                <div className="h-6 bg-gray-300 rounded w-3/4 mb-2">
                </div>
                <div className="h-4 bg-gray-300 rounded w-1/2">
                </div>
              </div>
              <div className="animate-pulse p-4 bg-gray-200 rounded-lg">
                <div className="h-6 bg-gray-300 rounded w-3/4 mb-2">
                </div>
                <div className="h-4 bg-gray-300 rounded w-1/2">
                </div>
              </div>
            </>
          ) : (
            <>
              <InfoCard title="Assinatura Atual" items={infoCardData} />
              <SliderCard
                title="Itens Adicionais"
                sliderSettings={sliderSettings}
                items={increments}
                renderItem={(item) => <ItemCard item={item} />}
                status={subscription?.status}
              />
            </>
          )}
        </div>

        {/* Histórico de Pagamentos */}
        <Card className="p-6 mt-6 tracking-wide">
          <h2 className="font-semibold mb-4 uppercase tracking-wide text-blue-500 ">
            Histórico de Assinaturas
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-muted-foreground text-sm uppercase tracking-wide pb-2">
                  <th>Plano</th>
                  <th>Status</th>
                  <th>Período</th>
                  <th>Valor</th>
                  <th>Método de Pagamento</th>
                  <th>Parcelas</th>
                  <th className="text-center align-middle">Ações</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  Array(3)
                    .fill(0)
                    .map((_, index) => (
                      <tr key={index} className="animate-pulse">
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-3/4">
                          </div>
                        </td>
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-3/4">
                          </div>
                        </td>
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-3/4">
                          </div>
                        </td>
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-1/2">
                          </div>
                        </td>
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-1/2">
                          </div>
                        </td>
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-1/4">
                          </div>
                        </td>
                        <td className="py-4 text-center">
                          <div className="h-4 w-4 bg-gray-200 rounded-full mx-auto">
                          </div>
                        </td>
                      </tr>
                    ))
                ) : subscriptionHistory.length > 0 ? (
                  currentHistoryItems.map((payment, index) => (
                    <React.Fragment key={index}>
                      <tr className="border-b">
                        <td className="py-2">
                          {payment.items[0].name}
                          {payment.isCurrent && !payment.isUpgrade && (
                            <span className="ml-2 bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-1 rounded-md dark:bg-green-900 dark:text-green-300">
                              Atual
                            </span>
                          )}
                          {payment.isUpgrade && (
                            <span className="ml-2 text-xs font-medium me-2 px-2.5 py-1 rounded-md bg-orange-500 dark:bg-gradient-to-r dark:from-orange-900 dark:to-orange-600 text-white">
                              🔥 Upgrade
                            </span>
                          )}
                        </td>
                        <td className="py-2 capitalize">{t(`status.${payment.status}`)}</td>
                        <td className="py-2">
                          {[
                            formatDate(payment.currentPeriodStart),
                            formatDate(payment.currentPeriodEnd),
                          ].join(" - ")}
                        </td>
                        <td className="py-2">{formatCurrency(getTotal(payment.items))}</td>
                        <td className="py-2">{t(`payment.${payment.paymentMethod}`)}</td>
                        <td className="py-2">{payment.installments}</td>
                        <td className="py-2 text-center align-middle">
                          {payment.status !== "future" && (
                            <button
                              onClick={() =>
                                navigate(`/invoices/${payment.id}`, {
                                  state: {
                                    payment,
                                    hasActiveSubscription: subscription?.status === "active"
                                  }
                                })
                              }
                              title="Visualizar Detalhes"
                              className={`mx-auto  text-blue-500 hover:opacity-75 focus:outline-none
                                  ${payment.status === "future"
                                  ? "opacity-50 cursor-not-allowed"
                                  : ""
                                }
                                `}
                              disabled={payment.status === "future"}
                            >
                              <Eye size={18} />
                            </button>
                          )}
                        </td>
                      </tr>
                    </React.Fragment>
                  ))
                ) : (
                  <tr>
                    <td className="py-4 text-center" colSpan={6}>
                      Nenhuma assinatura encontrada
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Controles de Paginação (exibidos apenas se houver mais de uma página) */}
          {subscriptionHistory.length > itemsPerPage && (
            <div className="flex justify-center items-center mt-6 mb-4">
              <nav className="flex items-center gap-4">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-2xl px-3 py-1"
                >
                  <ArrowLeftIcon />
                </button>

                <span className="text-sm text-gray-700">
                  {currentPage} / {totalPages}
                </span>

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-2xl px-3 py-1"
                >
                  <ArrowRightIcon />
                </button>
              </nav>{" "}
            </div>
          )}
        </Card>
      </div>
      <PixPayments
        isOpen={!!paymentData && subscription?.paymentMethod === "pix"}
        payment={{
          pix: {
            amount: paymentData?.amount,
            expiresAt: paymentData?.due_at,
            identifier: paymentData?.identifier,
            qrCode: paymentData?.qr_code,
            url: paymentData?.url,
          }
        }}
        onClose={() => setPaymentData(null)}
      />
      <LiveSupportChat />
    </div>
  );
}

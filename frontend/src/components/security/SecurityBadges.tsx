import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { LockKeyhole, Shield } from "lucide-react";

export function SecurityBadges() {
  return (
    <div className="flex flex-col items-center gap-3 mt-2 w-full">
      <div className="flex items-center justify-center gap-6">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 transition-colors w-full">
              <Shield className="h-5 w-5" />
              <span className="font-medium">Pagamento 100% Seguro</span>
            </TooltipTrigger>
            <TooltipContent className="max-w-[280px] border border-blue-200 rounded-md p-2">
              <div className="space-y-2">
                <p className="font-medium">Seus dados estão protegidos com:</p>
                <ul className="text-sm space-y-1">
                  <li className="flex items-center gap-2">
                    <LockKeyhole className="h-3 w-3" />
                    Criptografia de ponta a ponta
                  </li>
                  <li className="flex items-center gap-2">
                    <Shield className="h-3 w-3" />
                    Certificados de segurança internacionais
                  </li>
                </ul>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="flex items-center justify-center gap-6">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <div className="group">
                <img
                  src="/images/pci-compliance.jpg"
                  alt="PCI Compliant"
                  className="h-10 w-auto opacity-90 group-hover:opacity-100 transition-all duration-300 group-hover:scale-105"
                />
                <p className="text-xs text-blue-600 mt-1 font-medium">PCI Compliance</p>
              </div>
            </TooltipTrigger>
            <TooltipContent side="bottom" className="border border-blue-200 rounded-md p-2">
              <p className="max-w-[200px] text-sm">
                Certificação PCI DSS - Padrão internacional que garante a segurança no processamento de pagamentos
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <div className="h-12 w-px bg-blue-200"></div>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <div className="group">
                <img
                  src="/images/ssl-secure.png"
                  alt="SSL Secure"
                  className="h-10 w-auto opacity-90 group-hover:opacity-100 transition-all duration-300 group-hover:scale-105"
                />
                <p className="text-xs text-blue-600 mt-1 font-medium">SSL Security</p>
              </div>
            </TooltipTrigger>
            <TooltipContent side="bottom" className="border border-blue-200 rounded-md p-2">
              <p className="max-w-[200px] text-sm">
                Certificado SSL - Sua conexão é criptografada e protegida contra ameaças
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}
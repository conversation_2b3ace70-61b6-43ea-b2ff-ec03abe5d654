import { PlanOption } from "@/types/plan-types";
import { useState } from "react";
import { RangeSlider } from "../ui/range-slider";

interface LeadsCapacityControlProps {
  planId: string;
  initialValue: number;
  options: PlanOption[];
  onChange?: (planId: string, quantity: number) => void;
}

export function LeadsCapacityControl({
  planId,
  initialValue,
  options,
  onChange,
}: LeadsCapacityControlProps) {
  const ranges = options.map(opt => ({
    min: opt.contacts_min,
    max: opt.contacts_max,
  }));

  const min = ranges[0].max
  const max = ranges[ranges.length - 1].max || min
  const [currentRange, setCurrentRange] = useState(ranges[0])
  const [value, setValue] = useState<number>(initialValue)

  // Encontra o range mais próximo do valor atual
  const findClosestRange = (val: number) => {
    return ranges.find(range => val >= range.min && val <= range.max)
  }

  // Atualiza o valor quando o usuário arrasta o slider
  const handleValueChange = (values: number[]) => {
    const newValue = values[0]
    const selectedRange = findClosestRange(newValue)
    if (!selectedRange) return

    if (selectedRange === currentRange) return

    setCurrentRange(selectedRange)

    const option = options.find(opt => opt.contacts_min <= newValue && opt.contacts_max >= newValue)
    if (!option) return

    onChange?.(planId, option.contacts_max)
    setValue(option.contacts_max)
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between text-sm mb-2">
        <span className="text-muted-foreground">Capacidade de leads</span>
        <div className="text-right">
          <span className="font-medium">{value.toLocaleString()}</span>
          <div className="text-sm text-green-600">
            {/* {totalPrice > 0 && (
              <span>
                + {formatCurrency(totalPrice)} /mês
              </span>
            )} */}
          </div>
        </div>
      </div>
      <div>
        <RangeSlider
          min={1}
          max={max}
          value={[value]}
          ranges={ranges}
          onSliderChange={handleValueChange}
          className="mt-2"
          disabled={min === max}
        />
      </div>
      {max > min && <div className="text-xs text-muted-foreground">
        Esse plano permite upgrade para até {max.toLocaleString()} contatos.
      </div>}
    </div>
  )
}
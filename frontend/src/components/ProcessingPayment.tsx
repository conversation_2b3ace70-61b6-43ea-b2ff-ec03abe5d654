import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { motion } from "framer-motion";
import { ProcessingHeader } from "./payment/processing/ProcessingHeader";
import { ProcessingIcon } from "./payment/processing/ProcessingIcon";
import { ProcessingSteps } from "./payment/processing/ProcessingSteps";
import { SecurityBadges } from "./security/SecurityBadges";
interface ProcessingPaymentProps {
    isOpen: boolean;
    onFinish: () => void;
}

const ProcessingPayment = ({ isOpen, onFinish }: ProcessingPaymentProps) => {
    const containerAnimation = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                staggerChildren: 0.25
            }
        }
    };

    const itemAnimation = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    return (
        <Dialog open={isOpen} onOpenChange={() => { }}>
            <DialogContent className="max-w-xl p-0 bg-transparent border-none" aria-describedby={undefined}>
                <style>
                    {`
          .dialog-overlay {
            background-color: rgba(255, 255, 255, 0.2) !important;
            backdrop-filter: blur(4px);
          }
          `}
                </style>
                <motion.div
                    variants={containerAnimation}
                    initial="hidden"
                    animate="visible"
                    className="w-full relative z-10 space-y-2"
                >
                    <Card className="p-8 space-y-6 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-slate-200/50 shadow-2xl">
                        <DialogTitle>
                            <motion.div
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                                className="flex justify-center"
                            >
                                <img
                                    src="/qi-plus-brand.png"
                                    alt="QI PLUS Logo"
                                    className="h-16 w-auto px-4 py-2"
                                />
                            </motion.div>
                        </DialogTitle>

                        <div className="text-center space-y-4">
                            <ProcessingIcon />
                            <ProcessingHeader itemAnimation={itemAnimation} />
                        </div>

                        <ProcessingSteps itemAnimation={itemAnimation} onFinish={onFinish} />

                        <motion.div
                            variants={itemAnimation}
                            className="text-center text-sm text-gray-500"
                        >
                            <p>Por favor, não feche esta janela</p>
                            <p>Estamos processando sua solicitação</p>
                        </motion.div>
                        <SecurityBadges />
                    </Card>
                </motion.div>
            </DialogContent>
        </Dialog>
    );
};

export default ProcessingPayment; 
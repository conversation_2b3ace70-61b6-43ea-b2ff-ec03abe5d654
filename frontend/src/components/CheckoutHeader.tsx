import { useAuth } from "@/contexts/auth/useAuth";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { cn } from "@/lib/utils";
import { Menu as MenuIcon, X } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import ThemeToggle from "./ThemeToggle";
import ProfileBubble from "./ProfileBubble";
import { Separator } from "./ui/separator";

export function CheckoutHeader() {
  const { currentStep, setCurrentStep, lastStepForm, steps, selectedPlan } =
    useCheckout();
  const { isAuthenticated, accessSystemUrl } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const NavigationLinks = () => (
    <>
      <Link
        onClick={() => setCurrentStep(0)}
        to="/"
        className={cn(
          "text-gray-600 hover:text-gray-900 transition-colors",
          currentStep === 0 && "text-[#0071e2] hover:text-[#0071e2]/80"
        )}
        title="Ver planos disponíveis"
      >
        Planos
      </Link>
      {selectedPlan && (
        <Link
          onClick={() => setCurrentStep(lastStepForm)}
          to="/"
          className={cn(
            "text-gray-600 hover:text-[#0071e2]/80 transition-colors",
            currentStep > 0 &&
            currentStep < steps.length &&
            "text-[#0071e2] hover:text-[#0071e2]/80"
          )}
          title="Ir para cadastro"
        >
          Cadastro
        </Link>
      )}
      {isAuthenticated && (
        <Link
          onClick={() => setCurrentStep(steps.length)}
          to="/"
          className={cn(
            "text-gray-600 hover:text-[#0071e2]/80 transition-colors",
            currentStep == steps.length &&
            "text-[#0071e2] hover:text-[#0071e2]/80"
          )}
          title="Ir para pagamento"
        >
          Pagamento
        </Link>
      )}
      {isAuthenticated && (
        <Link
          to="/subscription"
          className={cn(
            "text-gray-600 hover:text-[#0071e2]/80 transition-colors",
            currentStep == steps.length &&
            "text-[#0071e2] hover:text-[#0071e2]/80"
          )}
          title="Ir para assinatura"
        >
          Assinatura
        </Link>
      )}
    </>
  );

  const UserSection = ({ mobile = false }) =>
    isAuthenticated && <ProfileBubble mobile={mobile} />;

  return (
    <header className="w-full border-b border-gray-200 dark:border-gray-800 mb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <Link
            to="/"
            className="flex items-center"
            aria-label="Ir para página inicial"
          >
            <img
              src="/qi-plus-brand.png"
              alt="QI PLUS Logo"
              className="h-8 w-auto"
            />
          </Link>

          {/* Mobile Menu Button */}
          <button
            type="button"
            className="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
            onClick={toggleMobileMenu}
            aria-expanded={isMobileMenuOpen}
            aria-label="Menu principal"
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" aria-hidden="true" />
            ) : (
              <MenuIcon className="h-6 w-6" aria-hidden="true" />
            )}
          </button>

          <nav className="hidden md:flex items-center gap-8">
            <NavigationLinks />

            <ThemeToggle />
            <UserSection />
            {/* Acessar sistema, abrir em nova aba */}
            {accessSystemUrl && (
              <Link
                to={accessSystemUrl}
                className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#0071e2] hover:bg-[#0071e2]/80 transition-colors duration-300"
                title="Fazer login no sistema"
                target="_blank"
              >
                Acessar sistema
              </Link>
            )}
          </nav>
        </div>

        {/* Mobile Menu */}
        <div className={cn("md:hidden", isMobileMenuOpen ? "block" : "hidden")}>
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <nav className="flex flex-col gap-4">
              <NavigationLinks />

              <Separator />

              <ThemeToggle isMobile />
              <UserSection mobile />
              {/* Acessar sistema, abrir em nova aba */}
              {accessSystemUrl && (
                <Link
                  to={accessSystemUrl}
                  className="flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#0071e2] hover:bg-[#0071e2]/80 transition-colors duration-300"
                  title="Fazer login no sistema"
                  target="_blank"
                >
                  Acessar sistema
                </Link>
              )}
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
}

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface FloatCenterButtonProps {
  title: string;
  onClick: () => void;
  icon?: React.ReactNode;
  hidden?: boolean;
  disabled?: boolean;
}

export function FloatCenterButton({
  onClick,
  title,
  icon,
  disabled = false,
}: FloatCenterButtonProps) {
  return (
    <Button
      variant="outline"
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "fixed w-1/3 bottom-0 left-1/2 transform -translate-x-1/2 mb-4 px-4 py-2 bg-[#0071e2] hover:bg-[#0071e2]/90 shadow-lg text-white hover:text-white/90 rounded-full animate-fade-in",
        disabled && "opacity-50 cursor-not-allowed"
      )}
    >
      {title}
      {icon && (
        <span className="relative z-10 flex items-center justify-center gap-2">
          {icon}
        </span>
      )}
    </Button>
  );
}

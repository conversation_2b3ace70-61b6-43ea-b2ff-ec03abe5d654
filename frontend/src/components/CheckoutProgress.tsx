import { cn } from "@/lib/utils";
import { Check } from "lucide-react";

interface CheckoutProgressProps {
  currentStep: number;
  steps: string[];
  onCurrentStepChange?: (step: number) => void;
}

export function CheckoutProgress({ currentStep, steps, onCurrentStepChange }: CheckoutProgressProps) {
  const isLastStep = (index: number) => index === steps.length - 1;
  const stepDone = (index: number) => currentStep > index;
  const isCurrentStep = (index: number) => currentStep === index;

  return (
    <div className="flex items-center justify-between w-full max-w-3xl mx-auto mb-8">
      {steps.map((step, index) => (
        <span key={index} className="flex items-center flex-1" onClick={() => onCurrentStepChange?.(index)}>
          <div
            className={cn(
              "w-8 h-8 rounded-full flex items-center justify-center border-2",
              stepDone(index)
                ? "bg-green-500 border-green-500 text-white"
                : isCurrentStep(index)
                  ? "border-green-500 text-green-500"
                  : "border-gray-300 text-gray-300"
            )}
          >
            {stepDone(index) ? (
              <Check className="h-4 w-4" />
            ) : (
              <div className="w-2 h-2 rounded-full bg-current" />
            )}
          </div>
          {!isLastStep(index) && (
            <div
              className={cn(
                "flex-1 h-[2px] mx-2",
                stepDone(index)
                  ? "bg-green-500"
                  : "bg-gray-300"
              )}
            />
          )}
        </span>
      ))}
    </div>
  );
}
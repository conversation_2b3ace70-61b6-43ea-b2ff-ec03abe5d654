import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Check, Tag } from "lucide-react";
import { useState } from "react";

interface PromoCodeInputProps {
  onApply: (discount: number) => void;
}

const VALID_CODES = {
  "PROMO10": 10,
  "PROMO20": 20,
  "WELCOME15": 15
};

export function PromoCodeInput({ onApply }: PromoCodeInputProps) {
  const [code, setCode] = useState("");
  const [isApplied, setIsApplied] = useState(false);
  const { toast } = useToast();

  const handleApplyCode = () => {
    const discount = VALID_CODES[code as keyof typeof VALID_CODES];

    if (discount) {
      onApply(discount);
      setIsApplied(true);
      toast({
        title: "Cupom aplicado!",
        description: `Desconto de ${discount}% aplicado com sucesso!`,
        duration: 3000,
      });
    } else {
      toast({
        title: "Cupom inválido",
        description: "Este código promocional não é válido.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <div className="flex flex-col gap-2 mt-4">
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Input
            value={code}
            onChange={(e) => setCode(e.target.value.toUpperCase())}
            placeholder="Digite seu código promocional"
            className="pl-9"
            disabled={isApplied}
          />
          <Tag className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
        </div>
        <Button
          onClick={handleApplyCode}
          disabled={!code || isApplied}
          className="bg-[#0071e2] hover:bg-[#0071e2]/90"
        >
          Aplicar
        </Button>
      </div>
      {isApplied && (
        <p className="text-sm text-green-600 flex items-center gap-1">
          <Check className="h-4 w-4" />
          Cupom aplicado com sucesso!
        </p>
      )}
    </div>
  );
}
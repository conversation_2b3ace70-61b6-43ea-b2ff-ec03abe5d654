import { ArrowLeftIcon, ArrowRightIcon, QrCode, Receipt } from "lucide-react";
import { Invoice } from "@/types/invoice.types";
import { cn, formatCurrency } from "@/lib/utils";
import { t } from "@/lib/translations.helper";
import { Card } from "../ui/card";
import { useState } from "react";
import PixPayments from "../PixPayments";
import { formatDate } from "@/lib/date.utils";

interface InvoiceTableProps {
  invoices: Invoice[];
  loading: boolean;
  onOpenBoleto: (invoiceId: string) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
}

const InvoiceTable = ({
  invoices,
  loading,
  onOpenBoleto,
  currentPage,
  totalPages,
  onPageChange,
  itemsPerPage,
}: InvoiceTableProps) => {
  // Get current page items
  const indexOfLastItem = (currentPage * itemsPerPage) > invoices.length ? invoices.length : (currentPage * itemsPerPage);
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentInvoices = invoices.slice(indexOfFirstItem, indexOfLastItem).sort((a, b) => a.installment - b.installment);
  const [paymentData, setPaymentData] = useState<any>(null);

  const isPending = (invoice: Invoice) => invoice.status === "pending";
  const isBankSlip = (invoice: Invoice) => invoice.transaction_type === "boleto";
  const isPix = (invoice: Invoice) => invoice.transaction_type === "pix";
  const isCreditCard = (invoice: Invoice) => invoice.transaction_type === "credit_card";

  const payInvoice = (invoice: Invoice) => {
    if (isBankSlip(invoice)) {
      onOpenBoleto(invoice.id);
    } else if (isPix(invoice)) {
      setPaymentData(invoice);
    }
  };

  return (
    <Card className="p-6 mt-6 tracking-wide">
      <h2 className="font-semibold mb-4 uppercase tracking-wide text-blue-500 ">
        Pagamentos
      </h2>
      <div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-muted-foreground text-sm uppercase tracking-wide pb-2">
                <th className="pb-2">Parcela</th>
                <th className="pb-2">Status</th>
                <th className="pb-2">Valor</th>
                <th className="pb-2">Vencimento</th>
                <th className="pb-2">Método de Pagamento</th>
                <th className="pb-2 text-center align-middle">Ações</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                Array(3)
                  .fill(0)
                  .map((_, index) => (
                    <tr key={index} className="animate-pulse">
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </td>
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </td>
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </td>
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      </td>
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      </td>
                      <td className="py-4 text-center">
                        <div className="h-4 w-4 bg-gray-200 rounded-full mx-auto"></div>
                      </td>
                    </tr>
                  ))
              ) : currentInvoices.length > 0 ? (
                currentInvoices.map((invoice, index) => (
                  <tr key={invoice.id || index} className="border-b">
                    <td className="py-2">{invoice.installment}</td>
                    <td className="py-2 capitalize">{t(`invoice.status.${invoice.status}`)}</td>
                    <td className="py-2">
                      {formatCurrency(invoice?.amount)}
                    </td>
                    <td className={cn(
                      "py-2",
                      !isPending(invoice) && "dashed line-through"
                    )}>
                      {formatDate(invoice.due_at)}
                    </td>
                    <td className="py-2">{t(`payment.${invoice.transaction_type}`)}</td>
                    {isPending(invoice) && !isCreditCard(invoice) && (
                      <td className="py-2 text-center align-middle">
                        <button
                          onClick={() => payInvoice(invoice)}
                          title={isBankSlip(invoice) ? "Visualizar Boleto" : "Pagar"}
                          className="mx-auto text-primary hover:opacity-75 focus:outline-none dark:text-white"
                        >
                          {isBankSlip(invoice) ? (
                            <Receipt size={18} />
                          ) : (
                            <QrCode size={18} />
                          )}
                        </button>
                      </td>
                    )}
                  </tr>
                ))
              ) : (
                <tr>
                  <td className="py-4 text-center" colSpan={6}>
                    Nenhum registro de pagamento encontrado
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {invoices.length > itemsPerPage && (
          <div className="flex justify-between items-center mt-6 mb-4">
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Exibindo de {indexOfFirstItem + 1} a {indexOfLastItem} de {invoices.length} faturas
            </span>
            <nav className="flex items-center gap-4">
              <button
                onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
                className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-2xl px-3 py-1 dark:text-white dark:hover:text-gray-300"
              >
                <ArrowLeftIcon />
              </button>

              <span className="text-sm text-gray-700 dark:text-gray-300">
                {currentPage} / {totalPages}
              </span>

              <button
                onClick={() =>
                  onPageChange(Math.min(currentPage + 1, totalPages))
                }
                disabled={currentPage === totalPages}
                className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-2xl px-3 py-1 dark:text-white dark:hover:text-gray-300"
              >
                <ArrowRightIcon />
              </button>
            </nav>
          </div>
        )}
      </div>
      <PixPayments
        isOpen={!!paymentData && paymentData.transaction_type === "pix"}
        payment={{
          pix: {
            amount: paymentData?.amount,
            expiresAt: paymentData?.due_at,
            identifier: paymentData?.identifier,
            qrCode: paymentData?.qr_code,
            url: paymentData?.url,
          }
        }}
        onClose={() => setPaymentData(null)}
      />
    </Card>
  );
};

export default InvoiceTable;

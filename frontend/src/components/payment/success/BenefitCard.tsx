import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { useState } from "react";

interface BenefitCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  type: "launch" | "scale" | "shake";
  animation: {
    launch?: {
      y: number;
      opacity: number;
      transition: {
        duration: number;
        ease: string;
      };
    };
    scale?: {
      scale: number;
      rotate: number;
      opacity: number;
      transition: {
        duration: number;
        ease: string;
      };
    };
    shake?: {
      y: number;
      opacity: number;
      transition: {
        duration: number;
        ease: string;
      };
    };
  };
}
export const BenefitCard = ({
  icon: Icon,
  title,
  description,
  animation,
  type,
}: BenefitCardProps) => {
  const [isAnimating, setIsAnimating] = useState(false);

  const handleClick = () => {
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 1000);
  };

  return (
    <div
      onClick={handleClick}
      className="p-4 rounded-2xl bg-[#e9177c]/5 border border-[#e9177c]/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer dark:bg-[#e9177c]/10 dark:border-[#e9177c]/40"
    >
      <div className="flex items-center space-x-2">
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={isAnimating ? animation[type] : { scale: 1, opacity: 1 }}
          variants={animation}
          transition={{ duration: 0.5 }}
        >
          {title === "Garantia Premium" ? (
            <img
              src="/qi-plus-logo.png"
              alt="Garantia Premium"
              className="w-10 h-10"
            />
          ) : (
            <Icon
              className={`w-10 h-10 ${title === "Acesso Instantâneo" || title === "PDF"
                  ? "fill-transparent [stroke:url(#gradient)]"
                  : "text-[#e9177c]"
                }`}
            />
          )}
        </motion.div>

        <h3 className="font-semibold text-slate-900 dark:text-gray-100 text-lg">
          {title}
        </h3>
      </div>

      <p className="text-slate-600 dark:text-gray-300 mt-2">{description}</p>

      {/* SVG Gradient Definition */}
      <svg width="0" height="0" className="absolute">
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style={{ stopColor: "#e9177c" }} />
            <stop offset="100%" style={{ stopColor: "#f16434" }} />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
};

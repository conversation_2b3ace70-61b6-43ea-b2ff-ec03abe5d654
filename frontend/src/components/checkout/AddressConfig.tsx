import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useState } from "react";
import {
  FieldErrors,
  FieldValues,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";
import { CheckoutFormData } from "./types";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { Pencil } from "lucide-react";
import { AddressStep } from "./steps/AddressStep";

export function AddressConfig() {
  const { setToBilling, form, toBilling } = useCheckout();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const { register, formState: { errors } } = form;

  const handleToggle = (checked: boolean) => {
    form.trigger().then((valid) => {
      if (valid) {
        setToBilling(checked);
        if (!checked) {
          setIsDialogOpen(true);
        }
      }
    });
  };

  const handleCloseDialog = () => {
    form.clearErrors();
    if (!isEditing) {
      setToBilling(!toBilling);
    }
    setIsDialogOpen(false);
    setIsEditing(false);
  };

  const handleConfirmDialog = () => {
    form
      .trigger()
      .then((onValid) => {
        if (onValid) {
          setIsDialogOpen(false);
          setIsEditing(false);
        }
      })
      .catch((error) => {
        console.log("error", error);
      });
  };

  return (
    <div className="flex items-center space-x-2">
      <Switch
        id="billingAddress"
        checked={toBilling}
        onCheckedChange={handleToggle}
      />
      <Label htmlFor="billingAddress">
        Utilizar meu endereço para fatura do cartão
      </Label>
      {!toBilling && (
        <Button
          variant="link"
          onClick={() => setIsEditing(true)}
          className="ml-2 text-sm text-gray-500 hover:text-gray-700 transition-colors"
        >
          <Pencil /> Editar endereço de fatura
        </Button>
      )}

      <Dialog
        open={isDialogOpen || isEditing}
        onOpenChange={(open) => !open && handleCloseDialog()}
      >
        <DialogContent className="sm:max-w-md w-full overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Coloque o endereço de fatura</DialogTitle>
            <DialogDescription>
              Os dados do endereço de fatura serão utilizados para cobranças.
            </DialogDescription>
          </DialogHeader>

          <AddressStep
            title=""
            toBilling={true}
          />

          <DialogFooter className="sm:justify-end">
            <Button
              type="button"
              variant="destructive"
              onClick={handleCloseDialog}
            >
              Cancelar
            </Button>
            <Button
              type="button"
              variant="secondary"
              onClick={handleConfirmDialog}
            >
              Confirmar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

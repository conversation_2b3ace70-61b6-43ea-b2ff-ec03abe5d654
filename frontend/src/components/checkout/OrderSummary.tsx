import { installments } from '@/config/payment';
import { useCheckout } from '@/contexts/checkout/useCheckout';
import { formatCurrency, formatCurrencyToShow } from '@/lib/utils';
import { useAuth } from '@/contexts/auth/useAuth';
import { t } from '@/lib/translations.helper';
import { getBasePrice, getCurrentPlanOnMonthly, getTotalPrice, isPartialUpgrading } from '@/lib/plan.utils';
import { calculateYearlyBillingDate, calculateYearlyInstallmentsAmount } from '@/lib/subscription.utils';
import { PaymentMethod } from './types';
import { addMonths, formatDate } from '@/lib/date.utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { HelpCircle } from 'lucide-react';

export function OrderSummary() {
  const { subscription } = useAuth();
  const {
    selectedPlan,
    additionalsCalculated,
    baseSubtotal,
    total,
    subtotal,
    discount,
    isUpgrade,
    isPartialUpgrade,
    isDowngrade,
    upgradeCredit,
    currentPlan,
    isCurrentYearly,
    plans,
    form,
  } =
    useCheckout();

  const isCreditCard = form.getValues('paymentMethod') === PaymentMethod.CREDIT_CARD;
  // const billingDay = isCreditCard ? new Date().getDate() : Number(form.watch('billingDay'));
  const billingDay = Number(form.watch('billingDay'));

  const { isYearly } = selectedPlan;

  const customFeatures = selectedPlan.customFeatures.map((feature) => {
    return {
      ...feature,
      additionals: feature.quantity - feature.included,
      totalPrice:
        (isYearly ? feature.yearlyPrice * 12 : feature.monthlyPrice) *
        (feature.quantity - feature.included),
    };
  });

  const selectedPlanInMonthly = getCurrentPlanOnMonthly(plans, selectedPlan);
  const maxInstallment = Math.max(...installments);
  const planPrice = getBasePrice(selectedPlan);
  const monthlyPrice = getTotalPrice(selectedPlanInMonthly) * 12;

  const changingBillingCycle = !!currentPlan && isCurrentYearly !== isYearly;

  // Calcular as datas
  const nextBillingDate = calculateYearlyBillingDate(billingDay);

  const endDate = subscription?.endDate ? new Date(subscription.endDate).getTime() : addMonths(nextBillingDate, maxInstallment - 1);
  const [firstInstallment, restInstallments] = calculateYearlyInstallmentsAmount({
    totalAmount: baseSubtotal,
    installments: maxInstallment,
    billingDay,
    creditDiscount: upgradeCredit || 0,
    paymentMethod: form.getValues('paymentMethod'),
    endDate,
    daysInCycle: isYearly ? 365 : 30,
  });

  const hasProportional = firstInstallment !== restInstallments;
  const totalInstallments = hasProportional ? maxInstallment - 1 : maxInstallment;

  return (
    <div className="rounded-lg border p-4 space-y-4">
      <div className="space-y-4 text-sm">
        <div className="flex justify-between text-sm border-b pb-1">
          <span>ITEM</span>
          <span>
            VALOR
            {' '}
            <span className="text-xs">
              ({isYearly ? 'ano' : 'mês'})
            </span>
          </span>
        </div>
        {(isUpgrade || isDowngrade) && (
          <span className="text-xs text-muted-foreground font-medium">
            Migrando de {currentPlan.name} {t(`interval.${isCurrentYearly ? 'yearly' : 'monthly'}`)}
            {' para '}
          </span>
        )}
        {isPartialUpgrade && (
          <span className="text-xs text-muted-foreground font-medium">
            Aumentando recursos do plano {currentPlan.name}
          </span>
        )}
        <div className="flex justify-between">
          <div className="flex flex-col">
            <span className="font-medium">{selectedPlan.name} {t(`interval.${isYearly ? 'yearly' : 'monthly'}`)} </span>
            <span className="text-xs font-normal text-green-600">
              {selectedPlan.leadsCount.toLocaleString()} leads
            </span>
          </div>
          <div className="flex flex-col">
            {isYearly && !isUpgrade && !isPartialUpgrade && selectedPlan.discount > 0 && (
              <span className="line-through text-muted-foreground text-xs">
                {formatCurrency(monthlyPrice)}
              </span>
            )}
            <span>{formatCurrency(planPrice)}</span>
          </div>
        </div>

        {customFeatures
          .filter((f) => f.additionals > 0)
          .map((feature) => (
            <div key={feature.id} className="flex justify-between text-sm">
              <div className="flex flex-col">
                <span className="font-medium text-xs">
                  {feature.quantity} {feature.name}
                </span>
                <span className="text-xs font-normal text-green-600">
                  {feature.included} incluídos + {feature.additionals} extras
                </span>
              </div>
              <span>{formatCurrency(feature.totalPrice)}</span>
            </div>
          ))}

        {additionalsCalculated
          .filter((c) => c.selected)
          .map((item) => (
            <div key={item.id} className="flex justify-between text-sm">
              <span>{item.name}</span>
              <span>{formatCurrency(item.price)}</span>
            </div>
          ))}

        {isYearly && subtotal != total && (isUpgrade || isDowngrade || isPartialUpgrade) && (
          <div className="flex justify-between items-center border-t pt-4">
            <div className="flex-1 ">Subtotal</div>
            <div className="text-right">
              <div className="flex flex-col">
                <p className="relative text-md text-green-600 font-medium">
                  {formatCurrency(subtotal)}
                </p>
              </div>
            </div>
          </div>
        )}

        {discount > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Desconto</span>
            <span>-{formatCurrency(discount)}</span>
          </div>
        )}

        {upgradeCredit > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Desconto no upgrade</span>
            <span>{formatCurrency(upgradeCredit)}</span>
          </div>
        )}

        <div className="border-b" />

        <div className="flex justify-between">
          <div className="flex-1 text-lg bg-clip-text font-medium">Total</div>
          <div className="text-right">

            <div className="flex flex-col">
              {/* Parcelamento PIX ou Boleto */}
              {isYearly && totalInstallments > 1 && !isCreditCard && (
                <>
                  {/* Total do plano anual */}
                  <p className="text-md font-medium text-green-600">
                    <span className="text-muted-foreground"> a vista </span>
                    {formatCurrency(total)}
                  </p>
                  <span className="text-muted-foreground">ou</span>

                  {/* Entrada proporcional (se existir) */}
                  {hasProportional && firstInstallment > 0 && (
                    <div className="text-xs font-normal text-muted-foreground">
                      <span className="text-muted-foreground">1x de </span>
                      <span className="text-green-600 text-sm font-bold">
                        {formatCurrency(firstInstallment)}
                      </span>
                      <div className="text-xs text-amber-600 font-medium mt-1">
                        * valor proporcional até {formatDate(nextBillingDate)}
                      </div>
                    </div>
                  )}

                  {/* Parcelas (após a entrada) */}
                  {firstInstallment > 0 ? (
                    <div className="text-xs font-normal mt-1">
                      <div className="text-green-600">
                        mais {totalInstallments}x de&nbsp;
                        <span className="text-sm font-bold">
                          {formatCurrency(restInstallments)}
                        </span>
                      </div>
                      <span className="text-xs text-amber-600">
                        * iniciando em {formatDate(nextBillingDate)}
                      </span>
                    </div>
                  ) : (
                    <div className="text-xs font-normal mt-1">
                      <div className="text-green-600">
                        {totalInstallments}x de&nbsp;
                        <span className="text-sm font-bold">
                          {formatCurrency(restInstallments)}
                        </span>
                      </div>
                      <span className="text-xs">
                        com primeira cobrança em {formatDate(nextBillingDate)}
                      </span>
                    </div>
                  )}
                </>
              )}

              {/* Parcelamento Cartão de Crédito */}
              {totalInstallments > 1 && isCreditCard && (
                <>
                  {/* Parcelas */}
                  <div className="text-xs text-green-600 font-normal mt-1">
                    {totalInstallments}x de&nbsp;
                    <span className="text-sm font-bold">
                      {formatCurrency(restInstallments)}
                    </span>
                  </div>
                  <span className="text-muted-foreground">ou</span>

                  {/* Total do plano anual */}
                  <p className="text-md font-medium text-white">
                    <span className="text-muted-foreground"> a vista </span>
                    {formatCurrency(total)}
                  </p>
                </>
              )}

              {/* Pagamento à vista */}
              {maxInstallment === 1 && (
                <p className="text-md text-green-600 font-medium mt-2">
                  Pagamento à vista: {formatCurrency(total)}
                </p>
              )}
            </div>

            {
              !isYearly && (
                <div className="flex flex-col">
                  <p className="relative text-md text-green-600 font-medium">
                    {formatCurrency(total)}
                  </p>
                </div>
              )
            }
          </div>
        </div>

        {changingBillingCycle && (
          <div className="flex justify-center align-center">
            <p className="text-xs text-amber-600 font-medium mr-2">
              * será cobrado e aplicado em {formatDate(subscription.endDate)}
            </p>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle size={16} />
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    O novo plano será ativado após o término da sua assinatura atual.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}

        {upgradeCredit > 0 && (
          <div className="flex justify-center text-green-600">
            <p className="text-xs text-amber-600 font-medium mt-1">
              * a partir de {formatDate(subscription.endDate)}
              {' - '}
              {formatCurrencyToShow(planPrice, isYearly)}
              /mês
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

import { Additional } from "@/types/additional-types";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { PaymentFields } from "../PaymentFields";
import { CheckoutFormData } from "../types";
import { useAuth } from "@/contexts/auth/useAuth";
import { Loading } from "@/components/Loading";

interface PaymentStepProps {
  isCompany: boolean;
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  additionals: Additional[];
  onAdditionalsChange: (additionals: Additional[]) => void;
}

export function PaymentStep({ isCompany, register, errors }: PaymentStepProps) {
  const { isRegistering } = useAuth();

  if (isRegistering) {
    return (
      <div className="flex flex-col items-center justify-center gap-4 min-h-[200px]">
        <h3 className="text-lg font-medium">
          Verificando suas informações com segurança. Um instante, por favor.
        </h3>
        <Loading />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <PaymentFields
        isCompany={isCompany}
        register={register}
        errors={errors}
      />
    </div>
  );
}

import { AccountTypeSelection } from "../AccountTypeSelection";

interface AccountTypeStepProps {
  title: string;
  isCompany: boolean | null;
  onTypeChange: (value: boolean) => void;
  disabled: boolean;
}

export function AccountTypeStep({ title, isCompany, onTypeChange, disabled }: AccountTypeStepProps) {
  return <AccountTypeSelection title={title} isCompany={isCompany} onTypeChange={onTypeChange} disabled={disabled} />;
}
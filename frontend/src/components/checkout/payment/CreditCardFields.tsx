import { MaskedInput } from "@/components/forms/MaskedInput";
import { InputMessageError } from "@/components/InputMessageError";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { installments } from "@/config/payment";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { formatCurrency, formatCurrencyToShow, isValidCreditCardNumber } from "@/lib/utils";
import { HelpCircle } from "lucide-react";
import { FieldErrors, useFormContext, UseFormRegister } from "react-hook-form";
import { CheckoutFormData } from "../types";
import { CreditCardDisplay } from "./CreditCardDisplay";

interface CreditCardFieldsProps {
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

export function CreditCardFields({ register, errors }: CreditCardFieldsProps) {
  const { isYearly, total } = useCheckout();
  const { setValue } = useFormContext<CheckoutFormData>();

  return (
    <div className="credit_card-fields space-y-4" data-method="credit_card">
      <CreditCardDisplay />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <MaskedInput
          inputId="cardNumber"
          title="Número do cartão"
          mask="9999 9999 9999 9999"
          placeholder="0000 0000 0000 0000"
          maxLength={19}
          required={true}
          message="Número do cartão é obrigatório"
          validate={(cardNumber) => {
            if (
              !isValidCreditCardNumber(
                cardNumber.toString()?.replace(/\D/g, "")
              )
            )
              return "O número não corresponde a um cartão válido";
          }}
        />

        <div>
          <Label htmlFor="cardHolderName">Nome no Cartão</Label>
          <Input
            id="cardHolderName"
            {...register("cardHolderName", {
              required: {
                value: true,
                message: "Nome no cartão é obrigatório",
              },
            })}
            placeholder="Como está escrito no cartão"
          />
          <InputMessageError error={errors.cardHolderName?.message} />
        </div>
      </div>


      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <MaskedInput
          inputId="cardExpiry"
          title="Data de Validade"
          mask="99/99"
          placeholder="MM/AA"
          maxLength={5}
          required={true}
          message="Data de validade é obrigatória"
          pattern={/^(0[1-9]|1[0-2])\/([0-9]{2})$/}
          patternMessage="Formato inválido (MM/AA)"
        />
        <div>
          <Label htmlFor="cardCvc">
            CVV
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="ml-1 inline-block h-4 w-4 cursor-help text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Código de segurança do cartão (3 dígitos)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Label>
          <Input
            id="cardCvc"
            {...register("cardCvc", {
              required: {
                value: true,
                message: "Código de segurança é obrigatório",
              },
              pattern: {
                value: /^[0-9]{3,4}$/,
                message: "Código de segurança inválido",
              },
            })}
            maxLength={4}
            className="w-full"
          />
          <InputMessageError error={errors.cardCvc?.message} />
        </div>
      </div>
    </div>
  );
}

import { useFormContext, useWatch } from "react-hook-form";
import { CheckoutFormData } from "../types";

export function CreditCardDisplay() {
  const { control } = useFormContext<CheckoutFormData>();

  const cardNumber = useWatch({
    name: "cardNumber",
    control,
    defaultValue: "•••• •••• •••• ••••",
  });

  const cardHolderName = useWatch({
    name: "cardHolderName",
    control,
    defaultValue: "NOME NO CARTÃO",
  });

  const cardExpiry = useWatch({
    name: "cardExpiry",
    control,
    defaultValue: "••/••",
  });

  const formatCardNumber = (number: string) => {
    if (!number) return "•••• •••• •••• ••••";
    const digits = number.replace(/\D/g, "");
    const groups = digits.match(/.{1,4}/g) || [];
    return groups.join(" ").padEnd(19, "•");
  };

  return (
    <div className="w-[340px] h-[200px] mx-auto mb-6 perspective">
      <div className="relative w-full h-full text-white">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-600 to-gray-800 rounded-xl p-6 shadow-lg">
          <div className="h-full flex flex-col justify-between">
            <div className="flex justify-between items-start">
              <div className="w-12 h-8 bg-yellow-400 rounded-md" />
            </div>

            <div className="space-y-4">
              <div className="text-xl tracking-widest font-mono">
                {formatCardNumber(cardNumber)}
              </div>

              <div className="flex justify-between items-end">
                <div className="space-y-1">
                  <p className="text-xs text-gray-300">Nome no Cartão</p>
                  <p className="font-medium tracking-wider">
                    {cardHolderName
                      ? cardHolderName.toUpperCase()
                      : "NOME NO CARTÃO"}
                  </p>
                </div>

                <div className="text-right space-y-1">
                  <p className="text-xs text-gray-300">Validade</p>
                  <p className="font-medium tracking-wider">
                    {cardExpiry || "••/••"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

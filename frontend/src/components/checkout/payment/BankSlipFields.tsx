import { InputMessageError } from "@/components/InputMessageError";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { installments } from "@/config/payment";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { FieldErrors, useFormContext, UseFormRegister } from "react-hook-form";
import { CheckoutFormData } from "../types";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCircle } from "lucide-react";

interface BankSlipFieldsProps {
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

export function BankSlipFields({ register, errors }: BankSlipFieldsProps) {
  return (
    <div className="boleto-fields space-y-4" data-method="boleto">
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium">Dados do Boleto</h4>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <HelpCircle className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  Você receberá o boleto para pagamento após finalizar o pedido
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="rounded-lg border border-muted p-4 bg-muted/10">
          <p className="text-sm text-muted-foreground">
            Após confirmar o pedido, você receberá um boleto bancário para realizar o
            pagamento. O boleto estará disponível para pagamento na sua assinatura e também será enviado
            para o seu e-mail.
          </p>
        </div>
      </div>
    </div>

  );
}

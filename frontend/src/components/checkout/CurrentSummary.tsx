import { useCheckout } from '@/contexts/checkout/useCheckout';
import { formatCurrency } from '@/lib/utils';
import { useAuth } from '@/contexts/auth/useAuth';
import { t } from '@/lib/translations.helper';
import { calculateYearlyBillingDate, calculateYearlyInstallmentsAmount } from '@/lib/subscription.utils';
import { addMonths } from '@/lib/date.utils';

export function CurrentSummary() {
  const { subscription } = useAuth();
  const { installments = 1, startDate, billingInterval } = subscription;
  const today = new Date(startDate).getTime()
  const isYearly = billingInterval === 'yearly';
  const selectedPlan = subscription.items.find((item) => item.type === 'plan')!;
  const leadsCount = subscription.accountConfig.data.contacts_max;
  const {
    additionalsCalculated,
    form,
  } =
    useCheckout();

  const billingDay = Number(form.watch('billingDay'));

  const customFeatures = subscription.items
    .filter(({ type, quantity, included }) => type === 'addon' && quantity > included)
    .map((feature) => {
      return {
        ...feature,
        additionals: feature.quantity - feature.included,
      };
    });

  const planPrice = selectedPlan.totalPrice;

  // Calcular as datas
  const nextBillingDate = calculateYearlyBillingDate(billingDay, today);

  const [firstInstallment, restInstallments] = calculateYearlyInstallmentsAmount({
    totalAmount: subscription.items.map((item) => item.totalPrice).reduce((acc, item) => acc + item, 0),
    installments: installments,
    billingDay: subscription.billingDay,
    creditDiscount: 0,
    today: today,
    endDate: subscription?.endDate ? new Date(subscription.endDate).getTime() : addMonths(nextBillingDate, Number(form.getValues('installments')) - 1),
  });

  const total = firstInstallment + (restInstallments * (subscription.installments - 1));

  const hasProportional = firstInstallment !== restInstallments;
  const totalInstallments = hasProportional ? installments - 1 : installments;

  return (
    <div className="rounded-lg border p-4 space-y-4">
      <div className="space-y-4 text-sm">
        <div className="flex justify-between text-sm border-b pb-1">
          <span>ITEM</span>
          <span>
            VALOR
            {' '}
            <span className="text-xs">
              ({isYearly ? 'ano' : 'mês'})
            </span>
          </span>
        </div>
        <div className="flex justify-between">
          <div className="flex flex-col">
            <span className="font-medium">{selectedPlan.name} {t(`interval.${isYearly ? 'yearly' : 'monthly'}`)} </span>
            <span className="text-xs font-normal text-green-600">
              {leadsCount.toLocaleString()} leads
            </span>
          </div>
          <div className="flex flex-col">
            <span>{formatCurrency(planPrice)}</span>
          </div>
        </div>

        {customFeatures
          .map(({ id, name, quantity, included, totalPrice, additionals }) => (
            <div key={id} className="flex justify-between text-sm">
              <div className="flex flex-col">
                <span className="font-medium text-xs">
                  {quantity} {name}
                </span>
                <span className="text-xs font-normal text-green-600">
                  {included} incluídos + {additionals} extras
                </span>
              </div>
              <span>{formatCurrency(totalPrice)}</span>
            </div>
          ))}

        {additionalsCalculated
          .filter((c) => c.selected)
          .map((item) => (
            <div key={item.id} className="flex justify-between text-sm">
              <span>{item.name}</span>
              <span>{formatCurrency(item.price)}</span>
            </div>
          ))}

        <div className="border-b" />

        <div className="flex justify-between items-center">
          <div className="flex-1 text-lg bg-clip-text font-medium">Total</div>
          <div className="text-right">
            <div className="flex flex-col">
              {totalInstallments === 1 ? (
                <p className="text-md font-medium text-white">
                  <span className="text-muted-foreground"> a vista </span>
                  {formatCurrency(total)}
                </p>
              ) : (
                <>
                  {/* Entrada proporcional (se existir) */}
                  {hasProportional && (
                    <div className="text-xs font-normal text-muted-foreground">
                      <span className="text-muted-foreground">1x de </span>
                      <span className="text-green-600 text-sm font-bold">
                        {formatCurrency(firstInstallment)}
                      </span>
                    </div>
                  )}

                  {/* Parcelas (após a entrada) */}
                  <div className="text-xs text-amber-600 font-normal mt-1">
                    mais {totalInstallments}x de&nbsp;
                    <span className="text-sm font-bold">
                      {formatCurrency(restInstallments)}
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

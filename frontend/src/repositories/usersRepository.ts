import { db } from '@/config/firebase';
import { addDoc, collection, doc, getDoc, getDocs, query, setDoc, updateDoc, where } from 'firebase/firestore';


class UsersRepository {
  private readonly COLLECTION;

  constructor() {
    this.COLLECTION = collection(db, 'qiusers');
  }

  async create(data: any) {
    try {

      if (data.id) {
        const docRef = doc(this.COLLECTION, data.id);
        await setDoc(docRef, data);
        return { id: data.id, ...data };
      }

      const docRef = await addDoc(this.COLLECTION, data);
      return { id: docRef.id, ...data };
    } catch (error) {
      console.error(`Error adding ${this.COLLECTION}:`, error);
      throw error;
    }
  };

  async update(id: string, data: any) {
    try {
      const docRef = doc(this.COLLECTION, id);
      await updateDoc(docRef, data);
      return { id, ...data };
    } catch (error) {
      console.error(`Error updating ${this.COLLECTION}:`, error);
      throw error;
    }
  }

  async get() {
    try {
      const querySnapshot = await getDocs(this.COLLECTION);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data as any
        };
      });
    } catch (error) {
      console.error(`Error getting ${this.COLLECTION}:`, error);
      throw error;
    }
  };

  async find(id: string) {
    try {
      const docRef = doc(this.COLLECTION, id);
      const docSnapshot = await getDoc(docRef);
      if (!docSnapshot.exists()) {
        return null;
      }
      const data = docSnapshot.data();
      return { id: docSnapshot.id, ...data as any };
    } catch (error) {
      console.error(`Error finding ${this.COLLECTION}:`, error);
      throw error;
    }
  }

  async findByOwner(owner: string) {
    const querySnapshot = await getDocs(query(this.COLLECTION, where('owner', '==', owner)));
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return { id: doc.id, ...data as any };
    });
  }
}

export const usersRepository = new UsersRepository();

import { db } from '@/config/firebase';
import { Account } from '@/types/account';
import { addDoc, collection, CollectionReference, doc, getDoc, getDocs, limit, onSnapshot, query, updateDoc, where } from 'firebase/firestore';

class AccountsRepository {
  private readonly COLLECTION_PATH = 'accounts';
  private readonly COLLECTION: CollectionReference;

  constructor() {
    this.COLLECTION = collection(db, this.COLLECTION_PATH);
  }

  getDocRef(id: string) {
    return doc(db, `${this.COLLECTION_PATH}/${id}`);
  }

  async create(data: Account) {
    try {
      const docRef = await addDoc(this.COLLECTION, data);
      return { id: docRef.id, ...data };
    } catch (error) {
      console.error(`Error adding ${this.COLLECTION}:`, error);
      throw error;
    }
  };

  async update(id: string, data: Account) {
    try {
      const docRef = doc(this.COLLECTION, id);
      await updateDoc(docRef, { ...data });
      return { id, ...data };
    } catch (error) {
      console.error(`Error updating ${this.COLLECTION}:`, error);
      throw error;
    }
  }

  async get() {
    try {
      const querySnapshot = await getDocs(this.COLLECTION);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data as Account
        };
      });
    } catch (error) {
      console.error(`Error getting ${this.COLLECTION}:`, error);
      throw error;
    }
  };

  async find(id: string) {
    try {
      const docRef = doc(this.COLLECTION, id);
      const docSnapshot = await getDoc(docRef);
      if (!docSnapshot.exists()) {
        return null;
      }
      const data = docSnapshot.data();
      return { id: docSnapshot.id, ...data as Account };
    } catch (error) {
      console.error(`Error finding ${this.COLLECTION}:`, error);
      throw error;
    }
  }

  async findByEmail(email: string) {
    try {
      const querySnapshot = query(this.COLLECTION, where('email', '==', email), limit(1));

      return getDocs(querySnapshot).then(querySnapshot => {
        if (querySnapshot.empty) {
          return null;
        }
        const doc = querySnapshot.docs[0];
        const data = doc.data();
        return { id: doc.id, ...data as Account };
      });

    } catch (error) {
      console.error(`Error finding ${this.COLLECTION}:`, error);
      throw error;
    }
  }

  async findByOwner(id: string) {
    try {

      const querySnapshot = query(this.COLLECTION, where('owner', '==', id), limit(1));

      return getDocs(querySnapshot).then(querySnapshot => {
        if (querySnapshot.empty) {
          return null;
        }
        const doc = querySnapshot.docs[0];
        const data = doc.data();
        return { id: doc.id, ...data as Account };
      });

    } catch (error) {
      console.error(`Error finding ${this.COLLECTION}:`, error);
      throw error;
    }
  }

  listenAccount(id: string, onAccountChange: (account: Account | null) => void) {
    try {
      const docRef = doc(db, `${this.COLLECTION_PATH}/${id}`);

      return onSnapshot(docRef, (doc) => {
        if (!doc.exists()) {
          console.log(`Account ${id} does not exist`);
          onAccountChange(null);
          return;
        }

        const data = doc.data();
        if (!data) {
          console.log(`No data for account ${id}`);
          onAccountChange(null);
          return;
        }

        try {
          const account = {
            id: doc.id,
            ...data
          };
          onAccountChange(account as Account);
        } catch (error) {
          console.error(`Error parsing account data for ${id}:`, error);
          onAccountChange(null);
        }
      }, (error) => {
        console.error(`Error listening to account ${id}:`, error);
        onAccountChange(null);
      });

    } catch (error) {
      console.error(`Error setting up listener for account ${id}:`, error);
      throw error;
    }
  }
}

export const accountsRepository = new AccountsRepository();

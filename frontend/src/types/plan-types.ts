export interface PlanFeature {
  name: string;
  included: boolean;
  showOnCard?: boolean;
}

export interface PagarmeData {
  id: string;
  interval: "year" | "month";
  customerId: string;
}
export interface PlanOption {
  contacts_min: number;
  contacts_max: number;
  monthly_value: number;
  yearly_value: number;
  discount?: number;
  discountPercentage?: number;
}

export interface CalculatedPlanValue {
  savings: number;
  formattedSaving: string;
  price: number;
  formattedPrice: string;
  discount: number;
  formattedDiscount: string;
}

export interface UpgradeValue {
  currentPlanCredit: number;
  newPlanProportional: number;
  immediateCharge: number;
  nextBillingValue: number;
  daysRemaining: number;
  currentPlanValue: number;
  newPlanValue: number;
  totalPayed?: number;
}

export interface CustomFeature {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  quantity: number;
  included: number;
}

export interface Plan {
  uniqueId: string;
  id: string;
  name: string;
  tag: string;
  description: string;
  img: string;
  features: PlanFeature[];
  isPopular?: boolean;
  options: PlanOption[];
  customFeatures: CustomFeature[];
  originalFeatures: CustomFeature[];
  calculatedValues: CalculatedPlanValue;
  planData; // Dados do plano de origem
  pagarme: PagarmeData[];
  credit?: number; // Valor do crédito da assinatura atual
  isCurrentPlan?: boolean; // Indica se é o plano atual do usuário
  config: {
    order: number;
  };

  // Dados para calculo de valor
  leadsCount: number;
  discount: number;
  discountPercentage: number;
  isYearly: boolean;
}

export interface Customer{
    name: string,
    email: string
    code?:string
    document?:string
    document_type?:string
    type:string
    gender?:string
    address: Address
}

export interface Address{
    country:string
    city:string
    state:string
    street:string
    number:string
    complement?:string
    neighborhood:string
    postalCode:string
}

export interface Card{
    number:string
    holder_name:string
    holder_document:string
    exp_month:string
    exp_year:string
    cvv:string
    brand:string
}